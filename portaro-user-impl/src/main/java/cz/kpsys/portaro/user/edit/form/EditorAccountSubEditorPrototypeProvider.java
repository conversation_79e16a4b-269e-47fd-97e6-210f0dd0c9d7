package cz.kpsys.portaro.user.edit.form;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.edit.request.EditorAccountEditationRequest;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EditorAccountSubEditorPrototypeProvider implements ContextualProvider<Department, EditorAccountEditationRequest> {

    @Override
    public EditorAccountEditationRequest getOn(Department ctx) throws ItemNotFoundException {
        EditorAccountEditationRequest req = EditorAccountEditationRequest.ofEmpty();
        req.setEditLevel(Optional.of(EditLevel.WORST));
        return req;
    }
}
