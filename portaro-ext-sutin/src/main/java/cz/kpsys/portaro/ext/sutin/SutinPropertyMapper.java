package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.property.SutinProperty;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinPropertyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.MajetekZaznam;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SutinPropertyMapper {

    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull DepartmentAccessor departmentAccessor;

    public SutinProperty mapNewProperty(@NonNull SutinPropertyResponse property) {
        return SutinProperty.mapNewProperty(
                property,
                tryToFindRecordIdOfExistingPropertyRecordByElementId(property.propertyData().elementId()).orElse(null),
                tryToFindRecordIdOfExistingUserRecordByNumericElementId(getLatestUser(property.propertyRecords())).orElse(null),
                tryToFindRecordIdOfExistingRecordByTableId(property.propertyData().groupId().getId().toString()).orElse(null),
                getPropertyDivision(property));
    }

    private Department getPropertyDivision(@NonNull SutinPropertyResponse property) {
        // DIVISION
        Integer divisionId = getLatestDivision(property.propertyRecords());

        if (divisionId == null) {
            divisionId = SutinContants.NEW_PROPERTY_HOLDER_DIVISION_NUMBER;
        }

        Integer finalDivId = divisionId;
        Optional<? extends Department> division = ListUtil.findSingleMatching(
                departmentAccessor.getAll(),
                department -> department.getSigla().equals(finalDivId.toString()),
                Department.class,
                "Sigla(Division elementId)=" + divisionId
        );

        if (division.isPresent()) {
            return division.get();
        }

        throw new IllegalStateException();
    }

    private Integer getLatestDivision(List<MajetekZaznam> propertyRecords) {
        return propertyRecords.stream()
                .filter(propertyRecord -> propertyRecord.validTo().isEqual(LocalDate.of(3333, 3, 3)))
                .findFirst()
                .map(MajetekZaznam::divisionId)
                .orElse(null);
    }

    private Integer getLatestUser(List<MajetekZaznam> propertyRecords) {
        return propertyRecords.stream()
                .filter(propertyRecord -> propertyRecord.validTo().isEqual(LocalDate.of(3333, 3, 3)))
                .findFirst()
                .map(MajetekZaznam::userElementId)
                .orElse(null);
    }

    private Optional<UUID> tryToFindRecordIdOfExistingUserRecordByNumericElementId(@Nullable Integer elementId) {
        if (elementId == null) {
            return Optional.empty();
        }
        return sutinRecordDataLoader.getRecordIdByExternalId(elementId.toString(), SutinContants.ELEMENT_ID_FIELD_CODE, SutinContants.PERSON_FOND_ID);
    }


    private Optional<UUID> tryToFindRecordIdOfExistingPropertyRecordByElementId(@Nullable String elementId) {
        if (elementId == null) {
            return Optional.empty();
        }
        return sutinRecordDataLoader.getRecordIdByExternalId(elementId, SutinContants.ELEMENT_ID_FIELD_CODE, SutinContants.PROPERTY_FOND_ID);
    }

    private Optional<UUID> tryToFindRecordIdOfExistingRecordByTableId(String tableId) {
        return sutinRecordDataLoader.getRecordIdByExternalId(tableId, SutinContants.TABLE_ID_FIELD_CODE, SutinContants.PROPERTY_GROUP_FOND_ID);
    }
}
