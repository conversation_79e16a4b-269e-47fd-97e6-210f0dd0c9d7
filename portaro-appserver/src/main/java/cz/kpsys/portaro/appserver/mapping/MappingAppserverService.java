package cz.kpsys.portaro.appserver.mapping;

import cz.kpsys.portaro.appserver.*;

public interface MappingAppserverService {

    <E> E call(AppserverRequest<?> request, AppserverResponseHandler<E> responseMapper) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException;

    <E> E call(AppserverRequest<?> request, AppserverResponseHandler<E> responseMapper, AppserverErrorHandler<E> errorHandler) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException;

    <E> E call(AppserverRequest<?> request, Class<E> responseType, AppserverErrorHandler<E> errorHandler) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException;

}
