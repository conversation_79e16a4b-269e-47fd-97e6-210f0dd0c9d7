package cz.kpsys.portaro.record.link;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordFactory;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.SimpleFieldContainer;
import cz.kpsys.portaro.record.detail.TestingFondedFieldTypeFactory;
import cz.kpsys.portaro.record.detail.value.FieldPayload;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static cz.kpsys.portaro.record.detail.FieldTypeId.top;

@Tag("ci")
@Tag("unit")
public class SearchElsewhereItemTest {

    @Test
    public void shouldBuildTemplateWithIsbn() {
        UUID id = UUID.fromString("a41a35a5-1a5d-4dff-baa7-f21b3d7c47bf");
        Fond fond = Fond.testingMonography();
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(id, fond);
        String name = "Soil ecology";

        FieldContainer recordDetail = new SimpleFieldContainer();

        TestingFondedFieldTypeFactory fieldTypeFactory = TestingFondedFieldTypeFactory.forFond(fond);
        var d20 = fieldTypeFactory.datafieldType(top("d20")).createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        recordDetail.add(d20);
        d20.add(fieldTypeFactory.standardSubfieldType(top("d20").sub("a")).createFieldByParentId(recordIdFondPair, d20.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("978-0-521-43521-5"))));

        Record d = RecordFactory.testingMonography(id, 1234, name, recordDetail);
        SearchElsewhereItem item = new SearchElsewhereItem("Google Books", "http://books.google.com/books?vid=ISBN${isbn}");

        Assertions.assertEquals("Google Books", item.getName());
        Assertions.assertTrue(item.isShowable(d));
        Assertions.assertEquals("http://books.google.com/books?vid=ISBN$9780521435215", item.getUrl(d));
    }

    @Test
    public void shouldBuildTemplateWithName() {
        Record d = RecordFactory.testingMonography(UUID.fromString("a41a35a5-1a5d-4dff-baa7-f21b3d7c47bf"), 1234, "Soil ecology");
        SearchElsewhereItem item = new SearchElsewhereItem("Amazon", "http://www.amazon.com/gp/search?index=books&amp;linkCode=qs&amp;keywords={name}");

        Assertions.assertTrue(item.isShowable(d));
        Assertions.assertEquals("http://www.amazon.com/gp/search?index=books&amp;linkCode=qs&amp;keywords=Soil+ecology", item.getUrl(d));
    }

    @Test
    public void shouldNotBeShowableWhenNoIsbn() {
        Record d = RecordFactory.testingMonography(UUID.fromString("a41a35a5-1a5d-4dff-baa7-f21b3d7c47bf"), 1234, "Soil ecology");
        SearchElsewhereItem item = new SearchElsewhereItem("Google Books", "http://books.google.com/books?vid=ISBN${isbn}");

        Assertions.assertEquals("Google Books", item.getName());
        Assertions.assertFalse(item.isShowable(d));
    }
}
