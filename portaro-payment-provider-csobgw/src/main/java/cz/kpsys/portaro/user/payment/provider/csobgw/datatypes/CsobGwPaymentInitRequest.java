package cz.kpsys.portaro.user.payment.provider.csobgw.datatypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

import java.util.List;

@Value
public class CsobGwPaymentInitRequest {

    @JsonProperty("merchantId")
    @NonNull
    String merchantId;

    @JsonProperty("orderNo")
    @NonNull
    String orderNumber;

    @JsonProperty("dttm")
    @NonNull
    String timestamp;

    @JsonProperty("payOperation")
    @NonNull
    String payOperation;

    @JsonProperty("payMethod")
    @NonNull
    String payMethod;

    @JsonProperty("totalAmount")
    @NonNull
    Integer totalAmount;

    @JsonProperty("currency")
    @NonNull
    String currency;

    @JsonProperty("closePayment")
    @NonNull
    Boolean closePayment;

    @JsonProperty("returnUrl")
    @NonNull
    String returnUrl;

    @JsonProperty("returnMethod")
    @NonNull
    String returnMethod;

    @JsonProperty("cart")
    @NonNull
    List<CsobGwCartObject> cart;

    @JsonProperty("merchantData")
    @Nullable
    String merchantData;

    @JsonProperty("customerId")
    @Nullable
    String customerId;

    @JsonProperty("language")
    @NonNull
    String language;

    @JsonProperty("ttlSec")
    @Nullable
    Integer ttlSec;

    @JsonProperty("logoVersion")
    @Nullable
    Integer logoVersion;

    @JsonProperty("colorSchemeVersion")
    @Nullable
    Integer colorSchemeVersion;

    @JsonProperty("customExpiry")
    @Nullable
    String customExpiry;

    @JsonProperty("signature")
    @NonNull
    String signature;
}
