package cz.kpsys.portaro.formannotation.annotations.formfield;

import java.lang.annotation.*;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface EnabledFormFields {

    /**
     * Bean must be of type:
     *  - ContextualProvider&lt;Department, List&lt;String&gt;&gt;
     *  - FormFieldsNamesDepartmentedProviderResolver
     * @return Name of bean declaring provider of enabled fields names
     */
    String bean();

    boolean resolveOnlyExplicitlyInvolved();

    @Target({ElementType.FIELD})
    @Retention(RUNTIME)
    @Documented
    @interface Involve {}

}
