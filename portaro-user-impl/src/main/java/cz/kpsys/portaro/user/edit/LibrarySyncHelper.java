package cz.kpsys.portaro.user.edit;

import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.validation.uppercase.Uppercase;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Library;
import cz.kpsys.portaro.user.edit.command.DepartmentEditationMode;
import cz.kpsys.portaro.user.edit.command.LibraryEditationCommand;
import cz.kpsys.portaro.user.edit.command.ReaderAccountEditationCommand;
import cz.kpsys.portaro.user.edit.command.SimpleEditableList;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LibrarySyncHelper {

    @NonNull UserEditationFactory userEditationFactory;
    @NonNull SideThreadAuthenticationIsolator authIsolator;

    public Library update(@NonNull Library library, @NullableNotBlank String customName, @NonNull Department ctx) {
        boolean readableDepartmentsInSync = library.getReadableDepartments().contains(ctx);
        boolean nameInSync = library.getName() != null && library.getName().equals(getNameToSave(library, library.getSigla(), customName));
        boolean readerAccountInSync = !library.getReaderAccounts().isEmpty();

        if (readableDepartmentsInSync && nameInSync && readerAccountInSync) {
            return library; // nothing to update -> fast return ... when UserEditationApplier will support checking changes, this if can be removed
        }

        return authIsolator.getAuthenticated(ctx, currentAuth -> {
            return userEditationFactory.<LibraryEditationCommand, Library>ofExistingUser(library, ctx, currentAuth)
                    .apply(editationRequest -> {
                        if (!nameInSync) {
                            editationRequest.setInstitutionName(Optional.of(getNameToSave(null, library.getSigla(), customName)));
                        }
                        if (!readableDepartmentsInSync) {
                            editationRequest.setReadableDepartments(Optional.of(SimpleEditableList.of(ctx, DepartmentEditationMode.MERGE_DUPLICATES_OR_APPEND)));
                        }
                        if (!readerAccountInSync) {
                            editationRequest.setReaderAccounts(Optional.of(List.of(ReaderAccountEditationCommand.ofEmpty()))); // reader category and others will be set by UserEditationRequestApplier defaults
                        }
                        editationRequest.setActive(Optional.of(true));
                    })
                    .saveIfModified()
                    .user();
        });
    }

    public Library create(@NonNull @Uppercase String sigla, @NullableNotBlank String customName, @NonNull Department ctx) {
        return authIsolator.getAuthenticated(ctx, currentAuth -> {
            return userEditationFactory.ofNewLibrary(ctx, currentAuth)
                    .apply(editationRequest -> {
                        editationRequest.setInstitutionName(Optional.of(getNameToSave(null, sigla, customName)));
                        editationRequest.setLibrarySigla(Optional.of(sigla));
                        editationRequest.setReadableDepartments(Optional.of(SimpleEditableList.of(ctx, DepartmentEditationMode.MERGE_DUPLICATES_OR_APPEND)));
                        editationRequest.setReaderAccounts(Optional.of(List.of(ReaderAccountEditationCommand.ofEmpty()))); // reader category and others will be set by UserEditationRequestApplier defaults
                        editationRequest.setActive(Optional.of(true));
                    })
                    .saveIfModified()
                    .user();
        });
    }

    private static String getNameToSave(@Nullable Library existingLibrary, @NonNull String sigla, @NullableNotBlank String customName) {
        String stubName = "Knihovna %s".formatted(sigla);
        if (existingLibrary != null && existingLibrary.getName() != null && !existingLibrary.getName().equals(stubName)) { // we don't want to overwrite library's name (if not a stub name)
            return existingLibrary.getName();
        }
        if (customName != null) {
            return customName;
        }
        return stubName;
    }

}
