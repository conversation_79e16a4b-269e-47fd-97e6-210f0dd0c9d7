package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;

import java.util.List;

public interface FieldTypeListable<SUBFIELD_TYPE extends FieldType> {

    @NonNull
    default List<? extends SUBFIELD_TYPE> getSubfieldTypes() {
        return List.of();
    }

    @NonNull
    default SUBFIELD_TYPE getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull FieldTypeId fieldTypeId) {
        return getSubfieldTypeFor(fieldTypeId);
    }

    @NonNull
    default SUBFIELD_TYPE getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull String subfieldCode) {
        List<? extends SUBFIELD_TYPE> subfieldTypes = getSubfieldTypes();
        return subfieldTypes.stream()
                .filter(By.code(subfieldCode))
                .findFirst()
                .orElseThrow(() -> new FieldTypeNotDefinedException(this, subfieldCode, ListUtil.convertStrict(subfieldTypes, FieldType::getFieldTypeId)));
    }

    @NonNull
    default SUBFIELD_TYPE getSubfieldTypeFor(@NonNull FieldTypeId fieldTypeId) {
        List<? extends SUBFIELD_TYPE> subfieldTypes = getSubfieldTypes();
        return subfieldTypes.stream()
                .filter(By.type(fieldTypeId))
                .findFirst()
                .orElseThrow(() -> new FieldTypeNotDefinedException(this, fieldTypeId, ListUtil.convertStrict(subfieldTypes, FieldType::getFieldTypeId)));
    }

}
