package cz.kpsys.portaro.view.web.rest.exemplar;

import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.volume.*;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cz.kpsys.portaro.app.CatalogWebConstants.API_URL_PREFIX;
import static cz.kpsys.portaro.app.CatalogWebConstants.VOLUMES_URL_PART;

@RequestMapping(API_URL_PREFIX + VOLUMES_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class VolumeApiController extends GenericApiController {

    public static final String CREATE_PATH = "/create";
    public static final String EDIT_PATH = "/edit";

    @NonNull VolumeLoader volumeLoader;
    @NonNull Deleter<Volume> volumeDeleter;
    @NonNull VolumeCreator volumeCreator;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull VolumeUpdater volumeUpdater;

    @GetMapping
    @RateLimited("VolumeApiController.query")
    public List<Volume> query(@RequestParam("document") Record d) {
        return volumeLoader.getByDocumentId(d.getId());
    }


    @GetMapping("{volume}")
    public Volume get(@PathVariable("volume") Volume volume) {
        return volume;
    }

    @PostMapping(CREATE_PATH)
    public ActionResponse create(@RequestBody @ValidFormObject VolumeCreationRequest volumeRequest,
                                 @CurrentDepartment Department currentDepartment) {
        VolumeCreateCommand command = new VolumeCreateCommand(
                volumeRequest.document().getId(),
                volumeRequest.volumeNumber(),
                volumeRequest.issueQuantity(),
                volumeRequest.year(),
                volumeRequest.periodicity(),
                volumeRequest.periodicityMultiplier(),
                null,
                volumeRequest.description(),
                false
        );
        volumeCreator.create(command, currentDepartment);

        return FinishedActionResponse.ok();
    }

    @PostMapping(EDIT_PATH)
    public ActionResponse edit(@RequestBody @ValidFormObject VolumeEditationRequest request) {
        VolumeEditationCommand command = new VolumeEditationCommand(
                request.volume().getId(),
                request.document().getId(),
                request.volumeNumber(),
                request.issueQuantity(),
                request.year(),
                request.periodicity(),
                request.periodicityMultiplier(),
                request.volume().getDate(),
                request.description(),
                false,
                request.volume().getDeleteDate().orElse(null)
        );
        volumeUpdater.update(command);
        return FinishedActionResponse.ok();
    }

    @PostMapping("/delete")
    public ActionResponse delete(@RequestBody @ValidFormObject VolumeDeletionRequest volumeRequest) {
        return transactionTemplate.execute(_ -> {
            Volume volume = volumeLoader.getById(volumeRequest.getId());
            volumeDeleter.delete(volume);
            return FinishedActionResponse.ok();
        });
    }
}
