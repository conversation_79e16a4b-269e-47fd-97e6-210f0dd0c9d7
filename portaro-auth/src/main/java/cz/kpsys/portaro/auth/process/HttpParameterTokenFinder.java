package cz.kpsys.portaro.auth.process;

import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class HttpParameterTokenFinder implements TokenFinder<String> {

    private String httpParameterName;

    @Override
    public Optional<String> getTokenFromRequest(HttpServletRequest request) {
        String raw = request.getParameter(httpParameterName);
        return Optional.ofNullable(raw);
    }
}
