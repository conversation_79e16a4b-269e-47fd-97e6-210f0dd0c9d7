package cz.kpsys.portaro.messages.converter;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.messages.db.entity.MessageEntity;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.messages.dto.UserToThreadMessage;
import cz.kpsys.portaro.messages.dto.UserToUserMessage;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class MessageToEntityConverter implements Converter<Message, MessageEntity> {

    @Override
    public MessageEntity convert(@NonNull Message message) {
        return switch (message) {
            case UserToThreadMessage userToThreadMessage -> new MessageEntity(
                    userToThreadMessage.id(),
                    userToThreadMessage.department().getId(),
                    userToThreadMessage.topic().getId(),
                    userToThreadMessage.severity().getId(),
                    userToThreadMessage.senderUser().getId(),
                    null,
                    userToThreadMessage.creationEvent().getId(),
                    userToThreadMessage.confirmationNecessary(),
                    userToThreadMessage.thread().getId(),
                    userToThreadMessage.content(),
                    ObjectUtil.getIdOrNull(userToThreadMessage.contentType()),
                    userToThreadMessage.directoryId(),
                    message.creationDate(),
                    message.activationDate()
            );
            case UserToUserMessage userToUserMessage -> new MessageEntity(
                    userToUserMessage.id(),
                    userToUserMessage.department().getId(),
                    userToUserMessage.topic().getId(),
                    userToUserMessage.severity().getId(),
                    userToUserMessage.senderUser().getId(),
                    userToUserMessage.targetUser().getId(),
                    userToUserMessage.creationEvent().getId(),
                    userToUserMessage.confirmationNecessary(),
                    null,
                    userToUserMessage.content(),
                    ObjectUtil.getIdOrNull(userToUserMessage.contentType()),
                    userToUserMessage.directoryId(),
                    message.creationDate(),
                    message.activationDate()
            );
        };
    }
}
