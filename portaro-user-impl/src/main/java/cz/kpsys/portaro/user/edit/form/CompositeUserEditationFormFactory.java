package cz.kpsys.portaro.user.edit.form;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.form.Form;
import cz.kpsys.portaro.user.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CompositeUserEditationFormFactory implements UserEditationFormFactory {

    @NonNull UserFormFieldsFactory userFormFieldsFactory;
    @NonNull PersonFormFieldsFactory personFormFieldsFactory;
    @NonNull InstitutionFormFieldsFactory institutionFormFieldsFactory;
    @NonNull SoftwareFormFieldsFactory softwareFormFieldsFactory;
    @NonNull LibraryFormFieldsFactory libraryFormFieldsFactory;
    @NonNull FamilyFormFieldsFactory familyFormFieldsFactory;
    @NonNull FormSettingsChecker formSettingsChecker;

    @Override
    public Form createForm(@NonNull User user, UserAuthentication currentAuth, Department department) throws IllegalFormSettingsException {

        formSettingsChecker.checkAndThrowIfInvalid(user, currentAuth, department);

        return switch (user) {
            case Person person -> {
                Form userForm = assembleForm(person, currentAuth, personFormFieldsFactory, department);

                if (!user.isActive()) {
                    userForm.setHeader(Texts.ofMessageCoded("ctenar.FullRegistrationFormHeader"));
                    userForm.setFooter(Texts.ofMessageCoded("ctenar.FullRegistrationFormFooter"));
                }

                yield userForm;
            }
            case Family family -> assembleForm(family, currentAuth, familyFormFieldsFactory, department);
            case Library library -> assembleForm(library, currentAuth, libraryFormFieldsFactory, department);
            case Institution institution -> assembleForm(institution, currentAuth, institutionFormFieldsFactory, department);
            case Software software -> assembleForm(software, currentAuth, softwareFormFieldsFactory, department);
            default -> assembleForm(user, currentAuth, userFormFieldsFactory, department);
        };
    }

    private <USER extends User> Form assembleForm(@NonNull USER editedUser, UserAuthentication currentAuth, FormFieldsFactory<USER> formFieldsFactory, Department department) {
        Text formTitle = editedUser.isActive() ? Texts.ofMessageCoded("user.settings") : Texts.ofMessageCoded("registrace.registrace");
        return new Form("user-editation", formTitle)
                .withFields(formFieldsFactory.get(editedUser, department, currentAuth));
    }

}
