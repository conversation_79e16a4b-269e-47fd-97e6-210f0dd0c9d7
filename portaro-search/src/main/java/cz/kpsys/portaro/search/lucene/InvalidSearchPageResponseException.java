package cz.kpsys.portaro.search.lucene;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.object.SeveritedException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class InvalidSearchPageResponseException extends RuntimeException implements SeveritedException, UserFriendlyException {

    @Getter
    int severity = SeveritedException.SEVERITY_ERROR;

    @Getter
    @NonNull
    Text text;

    public InvalidSearchPageResponseException(@NonNull String message, @NonNull Text text) {
        super(message);
        this.text = text;
    }

    public static void assertValidResponseElementsCount(@NonNull Range requestedRange, @NonNull Integer actualPageSize, @NonNull Integer actualTotalElements, @NonNull String requestToLog, @NonNull String responseToLog) {
        if (!isLastPage(requestedRange, actualTotalElements) && !hasPageAllRequiredItems(requestedRange, actualPageSize, actualTotalElements)) {
            String userFriendlyMessage = "Search requested %s items in page, appserver found total %s items, but returned page with only %s items.".formatted(requestedRange.getPageSize(), actualTotalElements, actualPageSize);
            String exceptionMessage = userFriendlyMessage + "\nRequest\n%s,\nResponse\n%s".formatted(requestToLog, responseToLog);
            throw new InvalidSearchPageResponseException(exceptionMessage, Texts.ofNative(userFriendlyMessage));
        }
    }

    private static boolean isLastPage(@NonNull Range requestedRange, @NonNull Integer actualTotalElements) {
        int totalPages = (int) Math.ceil(actualTotalElements / (double) requestedRange.getPageSize());
        return requestedRange.getPageNumber() == totalPages;
    }

    private static boolean hasPageAllRequiredItems(@NonNull Range requestedRange, @NonNull Integer actualPageSize, @NonNull Integer actualTotalElements) {
        return Math.min(requestedRange.getPageSize(), actualTotalElements) == actualPageSize;
    }
}
