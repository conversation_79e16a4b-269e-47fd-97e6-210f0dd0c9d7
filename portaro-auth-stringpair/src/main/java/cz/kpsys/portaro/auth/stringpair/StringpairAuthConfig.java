package cz.kpsys.portaro.auth.stringpair;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.Authenticity;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.CurrentAuthResponse;
import cz.kpsys.portaro.auth.current.resolver.CompositeUserAuthenticationAuthenticationResolver;
import cz.kpsys.portaro.auth.process.AuthoritiedSuccessAuthentication;
import cz.kpsys.portaro.auth.switchuser.RepresentableUserSwitchingAuthenticator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.StringPairUserProvider;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.relation.RepresentableUserLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StringpairAuthConfig {

    @NonNull CompositeUserAuthenticationAuthenticationResolver compositeUserAuthenticationAuthenticationResolver;
    @NonNull List<Class<? extends AuthoritiedSuccessAuthentication>> authenticationEventSavingTypes;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull StringPairUserProvider<Department> authenticationStringPairUserProvider;
    @NonNull UserByBasicUserLoader userLoader;
    @NonNull RepresentableUserLoader representableUserLoader;
    @NonNull Converter<UserAuthentication, CurrentAuthResponse> currentAuthToResponseConverter;

    @Bean
    public StringpairAuthController stringpairAuthController() {
        return new StringpairAuthController(
                stringpairAuthenticator(),
                authenticationHolder,
                currentAuthToResponseConverter
        );
    }

    private Authenticator<StringPairAuthenticationRequest, ? extends AuthoritiedSuccessAuthentication> stringpairAuthenticator() {
        return new RepresentableUserSwitchingAuthenticator<>(
                new StringPairAuthenticator(authenticationStringPairUserProvider),
                representableUserLoader,
                userLoader
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerAuthenticationResolvers() {
        compositeUserAuthenticationAuthenticationResolver
                .withAuthenticatedUserResolverOf(StringPairAuthentication.class, StringPairAuthentication::getActiveUser)
                .withAdditionalAuthoritiesResolverOf(StringPairAuthentication.class, StringPairAuthentication::getAuthorities)
                .withAuthenticityResolverOf(StringPairAuthentication.class, auth -> Authenticity.ONE_CONFIDENTAL_FACTOR);
        authenticationEventSavingTypes.add(StringPairAuthentication.class);
    }

}
