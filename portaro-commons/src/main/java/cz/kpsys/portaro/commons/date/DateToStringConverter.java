package cz.kpsys.portaro.commons.date;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.text.SimpleDateFormat;
import java.util.Date;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DateToStringConverter implements Converter<Date, String> {

    @NonNull SimpleDateFormat dateFormat;

    @Override
    public String convert(@NonNull Date source) {
        return dateFormat.format(source);
    }

}