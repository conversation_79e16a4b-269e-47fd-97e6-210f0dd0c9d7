package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import org.springframework.core.convert.converter.Converter;

import java.util.UUID;

public interface ConverterRegisterer {

    <MODEL> ConverterRegisterer registerForIntegerId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, Integer> byIdLoadable);

    <MODEL> ConverterRegisterer registerForIntegerIdWithCustomFromStringConversion(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, Integer> byIdLoadable, Converter<String, MODEL> customFromStringConverter);

    <MODEL> ConverterRegisterer registerForLongId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, Long> byIdLoadable);

    <MODEL> ConverterRegisterer registerForStringId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, String> byIdLoadable);

    <MODEL> ConverterRegisterer registerForUuidId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, UUID> byIdLoadable);

    <MODEL> ConverterRegisterer registerForUuidIdWithCustomFromStringConversion(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, UUID> byUuidIdLoader, Converter<String, MODEL> customFromStringConverter);

    <MODEL> ConverterRegisterer registerForValue(Class<MODEL> modelClass);
}
