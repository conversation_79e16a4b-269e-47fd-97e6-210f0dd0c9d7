package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.comment.*;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.PageSearchLoader;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResolver;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CommentConfig {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull IdAndIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull SettingLoader settingLoader;
    @NonNull ConverterRegisterer converterRegisterer;

    @Bean
    public CommentApiController commentApiController() {
        return new CommentApiController(
                commentSearchLoader(),
                commentSaver(),
                commentDeleter(),
                commentToCommentResponseConverter());
    }


    @Bean
    public Codebook<Comment, String> commentLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(CommentEntity.class)
                .converted(new EntitiesToCommentsConverter(basicUserLoader))
                .build();
    }


    @Bean
    public Saver<Comment, Comment> commentSaver() {
        return saverBuilderFactory.<Comment, String>saver()
                .intermediateConverting(new CommentToEntityConverter())
                .withClearedCacheName(CommentEntity.class.getSimpleName())
                .build();
    }


    @Bean
    public Deleter<Comment> commentDeleter() {
        return modelBeanBuilder.hibernateSoftDeleter(CommentEntity.class, new CommentToEntityConverter()).build();
    }

    @Bean
    public Converter<Comment, CommentResponse> commentToCommentResponseConverter() {
        return new CommentToCommentResponseConverter();
    }


    @Bean
    public ParameterizedSearchLoader<MapBackedParams, String> commentIdSearchLoader() {
        PageSearchLoader<MapBackedParams, String, RangePaging> modelIdSearchLoader = new SpringDbCommentIdSearchLoader(jdbcTemplate, queryFactory);
        return modelBeanBuilder.idSearchLoader(MapBackedParams::createEmpty, modelIdSearchLoader).build();
    }


    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Comment> commentSearchLoader() {
        return modelBeanBuilder.modelSearchLoaderByIdSearchLoader(MapBackedParams::createEmpty, commentIdSearchLoader(), commentLoader()).build();
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForStringId(Comment.class, commentLoader());
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        final ContextualProvider<Department, @NonNull CommentStyle> commentStyleProvider = settingLoader.getDepartmentedProvider(SettingKeys.COMMENT_STYLE);

        permissionRegistry.add(CommentSecurityActions.COMMENT_ADD, (auth, ctx, comment) -> {
            if (comment.getCreator().isPresent() && !auth.getActiveUser().equals(comment.getCreator().get())) { //forbid creating comment in name of another user
                return PermissionResult.forbid(Texts.ofNative("Cannot add comment as another user"));
            }
            if (!commentStyleProvider.getOn(ctx).isEnabled()) { //vypnuty komentare
                return PermissionResult.disabledFeature(Texts.ofNative("Comments are disabled"));
            }
            if (commentStyleProvider.getOn(ctx).isEnabledForAnonyms()) { //komentare i pro neprihlasene
                return PermissionResult.allow();
            }
            //komentare pouze pro prihlasene
            return permissionFactory.currentEvidedAuthenticActive().can(auth, ctx, null);
        });

        permissionRegistry.add(CommentSecurityActions.COMMENTS_DELETE, permissionFactory.edit());

        permissionRegistry.add(CommentSecurityActions.COMMENT_DELETE, PermissionResolver.or(
                (auth, ctx, comment) -> {
                    if (comment.getCreator().isEmpty()) {
                        return PermissionResult.forbid(Texts.ofNative("Comment is anonymous, cannot delete by not-editor user"));
                    }
                    return permissionFactory.currentEvidedAuthenticActiveIsSubjectUser().can(auth, ctx, comment.getCreator().get());
                },
                permissionFactory.edit()
        ));
    }

}
