package cz.kpsys.portaro.inventory.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum InventoryState implements LabeledIdentified<Integer> {

    CREATED(0, Texts.ofCurlyBracesFormat("{inventory.state.Created}")),
    OPENED(1, Texts.ofCurlyBracesFormat("{inventory.state.Opened}")),
    CLOSED(2, Texts.ofCurlyBracesFormat("{inventory.state.Closed}"));

    public static final Codebook<InventoryState, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull Text text;
}
