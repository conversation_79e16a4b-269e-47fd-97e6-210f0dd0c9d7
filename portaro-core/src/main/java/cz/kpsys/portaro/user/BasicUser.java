package cz.kpsys.portaro.user;

import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.RIdentified;
import cz.kpsys.portaro.commons.object.Sequence;
import lombok.NonNull;

import java.util.UUID;

public interface BasicUser extends LabeledIdentified<Integer>, RIdentified<UUID> {

    /**
     * <PERSON><PERSON><PERSON>, ze user ma kladne id
     */
    String ROLE_EVIDED = "ROLE_EVIDED";
    String ROLE_READER = "ROLE_READER";
    String ROLE_LIBRARY = "ROLE_LIBRARY";
    String ROLE_SUPPLIER = "ROLE_SUPPLIER";
    String ROLE_LIBRARIAN = "ROLE_LIBRARIAN";
    String ROLE_ADMIN = "ROLE_ADMIN";
    String ROLE_ACTUATOR = "ROLE_ACTUATOR";
    String ROLE_SERVICEMAN = "ROLE_SERVICEMAN";
    String ROLE_EXTERNALLY_AUTHENTICATED = "ROLE_EXTERNALLY_AUTHENTICATED";
    String ROLE_SCOPE_PREFIX = "ROLE_FOR_";

    String CURRENT_USER_ALIAS = "current";

    int PRINTABLE_NAME_SORTER_MAX_LENGTH = 60;
    int PRINTABLE_NAME_FLAT_MAX_LENGTH = 60;

    boolean isEvided();

    static boolean isEvided(@NonNull Integer id) {
        return id >= 0;
    }

    static boolean isNotEvided(@NonNull Integer id) {
        return !isEvided(id);
    }

    static Integer createNewUserId() {
        return -Sequence.getNextNumber();
    }

}