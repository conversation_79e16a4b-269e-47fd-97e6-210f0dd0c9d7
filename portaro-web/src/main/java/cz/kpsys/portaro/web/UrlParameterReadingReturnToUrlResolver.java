package cz.kpsys.portaro.web;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.util.UrlUtils;
import cz.kpsys.portaro.web.server.ServerUrlConfiguration;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import jakarta.servlet.http.HttpServletRequest;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UrlParameterReadingReturnToUrlResolver implements UrlWebResolver {

    @NonNull String requestParameterWithUrl;
    @NonNull ServerUrlConfiguration serverUrlConfiguration;

    @NonNull
    public String resolve(@NonNull HttpServletRequest request) {
        String returnToUrl = request.getParameter(requestParameterWithUrl);
        if (StringUtil.isNullOrBlank(returnToUrl)) {
            returnToUrl = serverUrlConfiguration.getPublicServerUrl();
        }
        if (serverUrlConfiguration.isPublicUrlOnHttps()) {
            returnToUrl = UrlUtils.replaceHttpToHttps(returnToUrl);
        }
        return returnToUrl;
    }
}
