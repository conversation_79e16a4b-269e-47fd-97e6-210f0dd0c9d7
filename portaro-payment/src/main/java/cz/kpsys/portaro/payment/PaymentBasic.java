package cz.kpsys.portaro.payment;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.finance.AmountGroup;
import cz.kpsys.portaro.user.BasicUser;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

import static cz.kpsys.portaro.payment.PaymentState.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(onConstructor_ = @JsonCreator(mode = JsonCreator.Mode.DISABLED)) // "onConstructor_ = @JsonCreator DISABLED" musime definovat i presto, ze nedelame deserializaci (pouze serializaci pri hledani plateb, coz bychom meli btw. predelat na nejaky PaymentResponse) - jackson pri serializaci rve, ze ma 2 konstruktory a nevi jaky si vybrat ("Conflicting property-based creators: already had explicit creator")
@AllArgsConstructor
public class PaymentBasic extends BasicIdentified<Integer> implements Payment {

    @NonNull String provider;
    @NonNull BasicUser payer;
    @Nullable BasicUser cashier;
    @NonNull Department department;
    @NonNull AmountGroup<? extends TypedUserAmount> items;
    @NonNull Instant createDate;
    @Nullable @NonFinal Instant cancelDate;
    @Nullable @NonFinal Instant payDate;
    @Nullable @NonFinal Instant refundDate;
    @Nullable @NonFinal Instant timeoutDate;
    @Nullable @NonFinal String error;

    public static PaymentBasic createNow(@NonNull PayCommand command) {
        return new PaymentBasic(command.provider(), command.payer(), command.cashier(), command.department(), command.items(), Instant.now());
    }

    @NonNull
    @Override
    public PaymentState getState() {
        if (timeoutDate != null) {
            return TIMEOUTED;
        }
        if (refundDate != null) {
            return REFUNDED;
        }
        if (cancelDate != null) {
            return CANCELED;
        }
        if (payDate != null) {
            return PAID;
        }
        return CREATED;
    }

    @Override
    public boolean setNewState(PaymentState newState) {
        PaymentState currentState = getState();
        if (currentState == newState) { //nastavujeme stejny stav jako je ted -> nechame byt
            return false;
        }
        if (!newState.isImmediatelyAfter(currentState)) { //poradi newState nesmi byt stejne nebo nizsi jako je aktualni, muzeme jit pouze na bezprostredne dalsi
            throw new IllegalStateException(String.format("Cannot set new state %s while current state is %s", newState, currentState));
        }

        switch (newState) {
            case CANCELED -> setCancelDate(Instant.now());
            case TIMEOUTED -> setTimeoutDate(Instant.now());
            case PAID -> setPayDate(Instant.now());
            case REFUNDED, PARTIALLY_REFUNDED -> setRefundDate(Instant.now());
        }
        return true;
    }

    @Override
    public String toString() {
        return "Payment " + getState() + " (provider " + provider + ", items " + items + ", payer " + payer + ")";
    }

    @Override
    public @NonNull String getProvider() {
        return provider;
    }

    @Override
    public @NonNull BasicUser getPayer() {
        return payer;
    }

    @Nullable
    @Override
    public BasicUser getCashier() {
        return cashier;
    }

    @Override
    public @NonNull Department getDepartment() {
        return department;
    }

    @Override
    public @NonNull BigDecimal getSumToPay() {
        return items.getSum().abs();
    }

    @Override
    public List<? extends TypedUserAmount> getItems() {
        return items.getTransactions();
    }



    @NonNull
    @Override
    public Instant getCreateDate() {
        return createDate;
    }



    @Nullable
    @Override
    public Instant getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(Instant cancelDate) {
        this.cancelDate = cancelDate;
    }



    @Nullable
    @Override
    public Instant getPayDate() {
        return payDate;
    }

    public void setPayDate(Instant payDate) {
        this.payDate = payDate;
    }



    @Nullable
    @Override
    public Instant getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(Instant refundDate) {
        this.refundDate = refundDate;
    }



    @Nullable
    @Override
    public Instant getTimeoutDate() {
        return timeoutDate;
    }

    public void setTimeoutDate(Instant timeoutDate) {
        this.timeoutDate = timeoutDate;
    }



    @Nullable
    @Override
    public String getError() {
        return error;
    }

    @Override
    public void setError(String error) {
        this.error = error;
    }

    @Nullable
    @JsonIgnore
    @Override
    public String getProviderPaymentId() {
        return null;
    }
}
