<script lang="ts">
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import {exists} from 'shared/utils/custom-utils';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let icon: UIcons | null = null;
    export let title: string | null = null;
    export let additionalClasses = '';
    export let wrap = false;
</script>

<div class="search-toolbar-section {additionalClasses}">
    {#if exists(title) || exists(icon)}
        <div class="title-container">
            {#if exists(icon)}
                <UIcon {icon}/>
            {/if}

            {#if exists(title)}
                <span class="title">{title}:</span>
            {/if}
        </div>
    {/if}

    <div class="search-toolbar-section-content" class:is-wrapped={wrap}>
        <slot/>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .search-toolbar-section {
        display: flex;
        align-items: start;

        .title-container {
            display: flex;
            align-items: center;
            gap: @spacing-s;
            margin-right: @spacing-sm;

            .title {
                white-space: nowrap;
            }
        }

        .search-toolbar-section-content {
            display: flex;
            align-items: center;
            height: 100%;
            gap: @spacing-sm;
            column-gap: @spacing-sm;
            row-gap: @spacing-s;
            white-space: nowrap;

            &.is-wrapped {
                flex-wrap: wrap;
            }
        }
    }

    :global {
        .search-toolbar-section .section-icon {
            height: min-content;
        }
    }
</style>