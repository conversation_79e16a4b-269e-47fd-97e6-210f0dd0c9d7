package cz.kpsys.portaro.ncip.impl.convert;

import cz.kpsys.portaro.ncip.schema.BibliographicDescription;
import cz.kpsys.portaro.ncip.schema.BibliographicItemId;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import static cz.kpsys.portaro.record.RecordWellKnownFields.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DocumentToNcipBibliographicDescriptionConverter implements Converter<Record, BibliographicDescription> {

    @NonNull Converter<Record, BibliographicItemId> recordToBibliographicItemIdConverter;

    @Override
    public BibliographicDescription convert(@NonNull Record d) {
        BibliographicDescription bd = new BibliographicDescription();

        bd.setBibliographicItemId(recordToBibliographicItemIdConverter.convert(d));

        bd.setTitle(d.getName());

        bd.setAuthor(d.query(DocumentTitle.ResponsibilityStatement.QUERY_CODE).getRaw());

        bd.setPlaceOfPublication(d.query(DocumentPublicationOld.Place.QUERY_CODE).getRaw());

        bd.setPublisher(d.query(DocumentPublicationOld.Publisher.QUERY_CODE).getRaw());

        bd.setPagination(d.query(PhysicalDescription.NumberOfPages.QUERY_CODE).getRaw());

        return bd;
    }

}
