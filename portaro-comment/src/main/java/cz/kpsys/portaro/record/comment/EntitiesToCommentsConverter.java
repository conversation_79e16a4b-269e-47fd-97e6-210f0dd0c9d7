package cz.kpsys.portaro.record.comment;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.BatchFiller;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntitiesToCommentsConverter implements Converter<List<? extends CommentEntity>, List<Comment>> {

    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUserLoader;

    @Override
    public List<Comment> convert(@NonNull List<? extends CommentEntity> entities) {
        Map<? extends CommentEntity, BasicUser> entityToNullableCreatorMap = BatchFiller.of(basicUserLoader).loadNullable(entities, commentEntity -> commentEntity.getCreatorId().orElse(null));

        return ListUtil.convert(entities, dto -> new CommentImpl(
                dto.getId(),
                dto.getRecordId(),
                entityToNullableCreatorMap.get(dto),
                dto.getDate(),
                dto.getText(),
                dto.getDeleteDate().orElse(null)
        ));
    }
}
