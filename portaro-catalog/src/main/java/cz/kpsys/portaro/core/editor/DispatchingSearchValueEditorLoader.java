package cz.kpsys.portaro.core.editor;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.acceptableroot.AcceptableRootValueEditor;
import cz.kpsys.portaro.form.valueeditor.multipleacceptable.MultipleAcceptableValueEditor;
import cz.kpsys.portaro.form.valueeditor.numberrange.NumberRangeValueEditor;
import cz.kpsys.portaro.form.valueeditor.searchtext.SearchTextValueEditor;
import cz.kpsys.portaro.form.valueeditor.year.YearValueEditor;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.isbn.IsbnEditorFactory;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.search.view.SearchValueEditorLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DispatchingSearchValueEditorLoader implements SearchValueEditorLoader<Department> {

    @NonNull ByIdLoadable<@Nullable AllValuesProvider<? extends LabeledIdentified<?>>, ScalarDatatype> acceptableValuesProviderLoader;
    @NonNull ContextualProvider<Department, List<Location>> readableLocationsContextualProvider;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull DepartmentAccessor departmentAccessor;

    @Override
    public @NonNull ValueEditor<?, ?, ?> getSearchValueEditor(@NonNull UserAuthentication currentAuth,
                                                              @NonNull Department ctx,
                                                              @NonNull ScalarDatatype datatype) {
        if (datatype.equals(RecordConstants.Datatype.DOCUMENT_FOND)) {
            return MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllDocumentByAuth(currentAuth, ctx))
                    .withPlaceholder(Texts.ofMessageCoded("commons.SelectEllipsis"));
        }

        if (datatype.equals(RecordConstants.Datatype.AUTHORITY_FOND)) {
            return MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllAuthorityByAuth(currentAuth, ctx))
                    .withPlaceholder(Texts.ofMessageCoded("commons.SelectEllipsis"));
        }

        if (datatype.equals(RecordConstants.Datatype.FOND)) {
            return MultipleAcceptableValueEditor.getEmptyEditor(currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                    .withPlaceholder(Texts.ofMessageCoded("commons.SelectEllipsis"));
        }

        if (datatype.equals(ExemplarConstants.Datatype.LOCATION)) {
            return MultipleAcceptableValueEditor.getEmptyEditor(readableLocationsContextualProvider.getOn(ctx))
                    .withPlaceholder(Texts.ofMessageCoded("commons.SelectEllipsis"));
        }

        if (datatype.equals(CoreConstants.Datatype.DEPARTMENT)) {
            return AcceptableRootValueEditor.getEmptyEditor(departmentAccessor.getHierarchical(ctx));
        }

        if (datatype.equals(CoreConstants.Datatype.RANGE_YEAR)) {
            return NumberRangeValueEditor.getEmptyEditor();
        }

        if (datatype.equals(CoreConstants.Datatype.YEAR)) {
            return YearValueEditor.getEmptyEditor();
        }

        if (datatype.equals(RecordConstants.Datatype.ISBN_OR_ISSN)) {
            return IsbnEditorFactory.createDefault();
        }

        AllValuesProvider<? extends LabeledIdentified<?>> acceptableValuesProvider = acceptableValuesProviderLoader.getById(datatype);
        if (acceptableValuesProvider != null) {
            return MultipleAcceptableValueEditor.getEmptyEditor(acceptableValuesProvider)
                    .withPlaceholder(Texts.ofMessageCoded("commons.SelectEllipsis"));
        }

        return SearchTextValueEditor.getEmptyEditor()
                .addLocalization("metadataSelectLabel", Texts.ofMessageCoded("search.accessibility.SearchOperatorSelectMsg"))
                .addLocalization("mainInputLabel", Texts.ofMessageCoded("search.accessibility.SearchTermForField"));
    }
}