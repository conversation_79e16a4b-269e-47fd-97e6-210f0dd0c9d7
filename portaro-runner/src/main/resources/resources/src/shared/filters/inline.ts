import {join} from '../utils/array-utils';

export type InlineFilter = (array: any[], delimiter?: string, prefix?: string, suffix?: string) => string;

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace angular {
        interface IFilterService {
            // eslint-disable-next-line @typescript-eslint/prefer-function-type
            (name: 'inline'): InlineFilter;
        }
    }
}

/**
 * @ngdoc filter
 * @name inline
 * @module portaro.filters
 * @kind function
 *
 * @param {string} arr Array to inlinize
 * @param {string=} delimiter Delimiter. Default `, `
 * @param {string=} prefix Prefix
 * @param {string=} suffix Suffix
 *
 * @return {string} Inlined array
 *
 * @description
 * This filter is like {@link Array#join Array.join} function, but it optionally prepends prefix and append suffix to each element.
 */
export default function inlineFilter(): InlineFilter {
    return (arr, delimiter = ', ', prefix = '', suffix = '') => {
        if (!Array.isArray(arr)) {
            return null;
        }
        return join(arr, delimiter, prefix, suffix);
    };
}

inlineFilter.filterName = 'inline';
