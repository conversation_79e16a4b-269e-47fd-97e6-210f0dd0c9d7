import valueEditorModule from '../../../value-editors.base.module';
import {createTestContext} from '../../../../../test-utils/utils';
import {
    createEditor, getErrorMessageElementByErrorType,
    getInputElement, setEditorModelValue, waitForCurrentState, waitForEvent, waitForNextState,
} from '../../../test-utils/utils';
import IInjectorService = angular.auto.IInjectorService;
import {waitForElementToBeRemoved} from '@testing-library/svelte';
import {AngularManualInjector} from 'core/injector';

describe('boolean-value-editor', () => {

    let testContext;

    function clickOnSwitch(switchElement: HTMLInputElement) {
        switchElement.click();
    }

    beforeEach(() => {
        angular.mock.module(valueEditorModule);

        inject(/*@ngInject*/ ($injector: IInjectorService) => {
            testContext = createTestContext(new AngularManualInjector($injector));
        });
    });


    it('should render editor', async () => {
        const {container, componentInstance, unmount} = await createEditor<'boolean', boolean>('boolean', testContext);

        expect(componentInstance).toBeTruthy();
        expect(getInputElement<HTMLInputElement>(container)).toBeTruthy();

        unmount();
    });

    it('should not render editorId', async () => {
        const {container, unmount} = await createEditor<'boolean', boolean>('boolean', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('data-id')).toBeFalsy();

        unmount();
    });

    it('should render editorName', async () => {
        const {container, unmount} = await createEditor<'boolean', boolean>('boolean', testContext);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('name')).toMatch(new RegExp('^boolean_[a-fA-F0-9-_]+'));

        unmount();
    });

    it('should render editorId and editorName', async () => {
        const {container, unmount} = await createEditor<'boolean', boolean>('boolean', testContext, null, {editorId: 'test-id', editorName: 'test-name'});

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('data-id')).toBe('test-id');
        expect(inputElement.getAttribute('name')).toBe('test-name');

        unmount();
    });

    it('should change model on input', async () => {
        const {container, componentInstance, unmount} = await createEditor<'boolean', boolean>('boolean', testContext, true);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(componentInstance.model).toBe(true);
        expect(inputElement.getAttribute('aria-checked')).toBe('true');
        clickOnSwitch(inputElement);

        const newModelValue: boolean = await waitForEvent(componentInstance, 'model-change');
        expect(newModelValue).toBe(false);
        expect(componentInstance.model).toBe(false);
        expect(inputElement.getAttribute('aria-checked')).toBe('false');

        unmount();
    });

    it('should change value if model changed', async () => {
        const {container, componentInstance, unmount} = await createEditor<'boolean', boolean>('boolean', testContext, false);

        const inputElement = getInputElement<HTMLInputElement>(container);
        expect(inputElement.getAttribute('aria-checked')).toBe('false');

        await setEditorModelValue(componentInstance, true);
        expect(inputElement.getAttribute('aria-checked')).toBe('true');

        unmount();
    });

    it('should have working input disabling', async () => {
        const {container, unmount} = await createEditor<'boolean', boolean>('boolean', testContext, null, {
            isDisabled: true
        });

        expect(getInputElement<HTMLInputElement>(container).disabled).toBe(true);
        unmount();
    });

    it('should have default value of false', async () => {
        const {componentInstance, unmount} = await createEditor<'boolean', boolean>('boolean', testContext, null);

        const state = await waitForCurrentState(componentInstance);
        expect(componentInstance.model).toBeFalse();
        expect(state.value).toBeFalse();

        unmount();
    });

    it('should have working assertTrue validation', async () => {
        const {container, componentInstance, unmount} = await createEditor<'boolean', boolean>('boolean', testContext, null, {
            validations: {assertTrue: true}
        });

        const controller = componentInstance.getFormController();
        const inputElement = getInputElement<HTMLInputElement>(container);
        controller.setTouched(); // to show errors

        let state = await waitForCurrentState(componentInstance);
        expect(componentInstance.model).toBe(null);
        expect(state.errors.includes('required')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeTruthy();


        clickOnSwitch(inputElement);
        state = await waitForNextState(componentInstance);
        expect(componentInstance.model).toBe(true);
        expect(state.errors.includes('required')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'required'), {container});
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeFalsy();


        clickOnSwitch(inputElement);
        state = await waitForNextState(componentInstance);
        expect(componentInstance.model).toBe(null);
        expect(state.errors.includes('required')).toBeTrue();
        expect(inputElement.getAttribute('aria-invalid')).toBe('true');
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeTruthy();

        clickOnSwitch(inputElement);
        state = await waitForNextState(componentInstance);
        expect(componentInstance.model).toBe(true);
        expect(state.errors.includes('required')).toBeFalse();
        expect(inputElement.getAttribute('aria-invalid')).toBe('false');
        await waitForElementToBeRemoved(getErrorMessageElementByErrorType(container, 'required'), {container});
        expect(getErrorMessageElementByErrorType(container, 'required')).toBeFalsy();

        unmount();
    });

    it('should have working model value substitution options.{trueValue, falseValue} - checkbox -> model', async () => {
        const {container, componentInstance, unmount} = await createEditor<'boolean', string>('boolean', testContext, null, {
            options: {trueValue: 'YES', falseValue: 'NO'}
        });

        const inputElement = getInputElement<HTMLInputElement>(container);
        clickOnSwitch(inputElement);

        let newModelValue = await waitForEvent(componentInstance, 'model-change');
        expect(componentInstance.model).toBe('YES');
        expect(newModelValue).toBe('YES');

        clickOnSwitch(inputElement);

        newModelValue = await waitForEvent(componentInstance, 'model-change');
        expect(componentInstance.model).toBe('NO');
        expect(newModelValue).toBe('NO');
        unmount();
    });


    it('should have working model value substitution options.{trueValue, falseValue} - model -> checkbox', async () => {
        const {container, componentInstance, unmount} = await createEditor<'boolean', string>('boolean', testContext, null, {
            options: {trueValue: 'YES', falseValue: 'NO'}
        });

        const inputElement = getInputElement<HTMLInputElement>(container);

        await setEditorModelValue(componentInstance, 'YES');
        expect(inputElement.getAttribute('aria-checked')).toBe('true');

        await setEditorModelValue(componentInstance, 'NO');
        expect(inputElement.getAttribute('aria-checked')).toBe('false');

        unmount();
    });

    it('should have working emptyAsNull option', async () => {
        const {container, componentInstance, unmount} = await createEditor<'boolean', boolean>('boolean', testContext, null, {
            options: {
                emptyAsNull: true,
            }
        });

        expect(componentInstance.model).toBeNull();

        const inputElement = getInputElement<HTMLInputElement>(container);
        clickOnSwitch(inputElement);

        const newModelValue = await waitForEvent(componentInstance, 'model-change');
        expect(componentInstance.model).toBeTrue();
        expect(newModelValue).toBeTrue();

        unmount();
    });

});
