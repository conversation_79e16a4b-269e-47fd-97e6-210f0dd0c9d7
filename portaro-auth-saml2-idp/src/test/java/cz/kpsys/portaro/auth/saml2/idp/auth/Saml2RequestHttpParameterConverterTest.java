package cz.kpsys.portaro.auth.saml2.idp.auth;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.kpsys.portaro.department.Department;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.Map;

@Tag("ci")
@Tag("unit")
class Saml2RequestHttpParameterConverterTest {

    private static ObjectMapper xmlMapper() {
        return new XmlMapper()
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .registerModule(new JavaTimeModule());
    }

    private static AuthnRequest authnRequest() {
        return new AuthnRequest(
                null,
                1,
                "https://katalog.cnb.cz/auth/saml2-idp/redirect-binding-sso",
                null,
                Instant.parse("2021-09-21T07:30:49Z"),
                "_08dc4c6deee466aa7eb1f2e942c32d56",
                null,
                null,
                "2.0",
                "https://shibboleth.cambridge.org/shibboleth-sp"
        );
    }

    @Test
    void shouldSupportAssertionConsumerServiceIndex() {
        Saml2RequestHttpParameterConverter converter = new Saml2RequestHttpParameterConverter(
                xmlMapper(),
                new IndexedConsumerServiceUrlRegister().withIssuer("https://shibboleth.cambridge.org/shibboleth-sp", Map.of(1, "https://shibboleth.cambridge.org/Shibboleth.sso/SAML2/POST"))
        );
        Saml2AuthRequest authRequest = converter.convertFromAuthnRequest(authnRequest(), Department.testingRoot());
        Assertions.assertEquals("https://shibboleth.cambridge.org/Shibboleth.sso/SAML2/POST", authRequest.getAssertionConsumerServiceURL());
    }

}