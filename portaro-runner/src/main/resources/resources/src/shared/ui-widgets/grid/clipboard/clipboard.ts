import type {Labeled, Valuable} from 'typings/portaro.be.types';
import {base64ToUtf, utfToBase64} from 'shared/utils/string-utils';
import {exists, isFunction} from 'shared/utils/custom-utils';

export type ClipboardCellData = Labeled & Valuable<Record<string, any>>;

export function createClipBoardItem({text, value}: ClipboardCellData): ClipboardItem {
    const textBlob = createPlainTextBlob(text);
    const json = JSON.stringify(value);
    const jsonBlob = createJsonBlob(json);
    const htmlBlob = createHtmlBlob({text, json});

    return new ClipboardItem({
        [textBlob.type]: textBlob,
        [htmlBlob.type]: htmlBlob,
        ...supportsCustomWebFormats() ? {[jsonBlob.type]: jsonBlob} : {},
    });
}

export async function resolveClipboardData(clipboardItems: ClipboardItems): Promise<string | ClipboardCellData['value']> {
    let clipboardItem = clipboardItems.find((item) => item.types.includes('web application/json'));
    if (exists(clipboardItem)) {
        return parseJsonBlob(clipboardItem);
    }

    clipboardItem = clipboardItems.find((item) => item.types.includes('text/html'));
    if (exists(clipboardItem)) {
        return parseHtmlBlob(clipboardItem);
    }

    clipboardItem = clipboardItems.find((item) => item.types.includes('text/plain'));
    if (exists(clipboardItem)) {
        return parsePlainTextBlob(clipboardItem);
    }

    return null;
}

function createPlainTextBlob(text: string) {
    return new Blob([text], {type: 'text/plain'});
}

function createJsonBlob(json: string) {
    return new Blob([json], {type: 'web application/json'});
}

function createHtmlBlob({text, json}: Record<'text' | 'json', string>) {
    const base64 = utfToBase64(json);
    const element = document.createElement('data');
    element.className = 'data-grid-cell';
    element.value = base64;
    element.textContent = text;
    const html = element.outerHTML;
    return new Blob([html], {type: 'text/html'});
}

function supportsCustomWebFormats() {
    // eslint-disable-next-line @typescript-eslint/unbound-method
    return 'supports' in ClipboardItem && isFunction(ClipboardItem.supports) && ClipboardItem.supports('web application/json');
}

async function parsePlainTextBlob(clipboardItem: ClipboardItem) {
    const textBlob = await clipboardItem.getType('text/plain');
    return textBlob.text();
}

async function parseJsonBlob(clipboardItem: ClipboardItem) {
    const jsonBlob = await clipboardItem.getType('web application/json');
    const json = await jsonBlob.text();
    return JSON.parse(json);
}

async function parseHtmlBlob(clipboardItem: ClipboardItem) {
    const jsonBlob = await clipboardItem.getType('text/html');
    const html = await jsonBlob.text();
    const parser = new DOMParser();
    const container = parser.parseFromString(html, 'text/html');
    const element = container.querySelector<HTMLDataElement>('data.data-grid-cell');
    const base64 = element.value;
    const json = base64ToUtf(base64);
    return JSON.parse(json);
}