package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondWorkCatalog;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.SutinRecordDataLoader;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.WorkCatalogIdMapping;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.WorkCatalogLinkDto;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class WorkCatalogLinkRecordEditationFiller {

    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull CacheDeletableById recordCache;
    @NonNull RecordEditationHelper recordEditationHelper;

    public RecordEditation fill(@NonNull WorkCatalogLinkDto workCatalogLink,
                                @NonNull RecordEditation recordEditation,
                                @NonNull Record userRecord,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        // TODO: add to definition
        recordEditationHelper.setStringTopFieldValue(workCatalogLink.rowId(), RowId.TYPE_ID, recordEditation);

        recordEditationHelper.setStringSubfieldValue(workCatalogLink.type().portaroVal(), true, FondWorkCatalog.WorkType.TYPE_ID, true, FondWorkCatalog.WorkType.Type.TYPE_ID, recordEditation);

        var catalogIdMapping = WorkCatalogIdMapping.map(workCatalogLink.katalogId());
        Optional<UUID> catalogItemId = sutinRecordDataLoader.getRecordIdByExternalId(catalogIdMapping.newId(), RowId.CODE, catalogIdMapping.fondId());
        if (catalogItemId.isPresent()) {
            var workCatalogItem = recordLoader.getById(catalogItemId.get());
            DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(workCatalogItem, ctx, currentAuth);
            recordEditationHelper.setRecordIdSubfieldValue(cmd, true, FondWorkCatalog.WorkCatalog.TYPE_ID, true, FondWorkCatalog.WorkCatalog.Main.TYPE_ID, recordEditation);
            recordCache.deleteFromCacheById(catalogItemId.get());
        } else {
            log.error("Cannot find price list item for {}", workCatalogLink);
        }

        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(userRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, FondWorkCatalog.Person.TYPE_ID, true, FondWorkCatalog.Person.Main.TYPE_ID, recordEditation);

        setDay(workCatalogLink.validFrom(), FondWorkCatalog.ValidFrom.Value.TYPE_ID, recordEditation);
        setDay(workCatalogLink.validTo(), FondWorkCatalog.ValidTo.Value.TYPE_ID, recordEditation);

        return recordEditation;
    }

    private void setDay(@Nullable LocalDate localDate, @NonNull FieldTypeId subfieldTypeId, @NonNull RecordEditation recordEditation) {
        if (localDate == null || SutinContants.isDateSkipped(localDate)) {
            return;
        }
        recordEditationHelper.setDateSubfieldValue(localDate, true, subfieldTypeId.existingParent(), true, subfieldTypeId, recordEditation);
    }

}
