import type {Ace} from 'ace-builds';
import type {Observable} from 'rxjs';
import type {FormControl} from '../value-editors/internal/forms/form-control';
import {fromEvent, merge, Subject} from 'rxjs';
import {debounceTime, distinctUntilChanged, filter, map} from 'rxjs/operators';
import {isDefined, isFunction} from '../utils/custom-utils';
import './ace-imports';
import * as aceImport from 'ace-builds';

export function aceEditor(inputElement: HTMLDivElement, {formControl, options}: AceFormControls) {
    const editor: Ace.Editor = aceImport.edit(inputElement);
    const session: Ace.EditSession = editor.getSession();
    const resizeObserver = new ResizeObserver(() => {
        editor.resize();
        editor.renderer.updateFull();
    });

    init(options);

    function init(opts) {
        resizeObserver.observe(inputElement);

        editor.on('change', onChange);
        editor.on('blur', onBlur);

        setOptions(opts);

        if (isDefined(opts.isFocused) && opts.isFocused) {
            editor.focus();
        }

        onLoad();
    }

    function onChange() {
        const newValue = session.getValue();
        const event = new CustomEvent('ace-input', {detail: newValue});
        inputElement.dispatchEvent(event);
    }

    function onBlur() {
        const newValue = session.getValue();
        const event = new CustomEvent('ace-blur', {detail: newValue});
        inputElement.dispatchEvent(event);
    }

    function onLoad() {
        const event = new CustomEvent('ace-init', {detail: editor});
        inputElement.dispatchEvent(event);
    }

    function setOptions(opts) {

        // enable 'Escape' key shortcut to unfocuse editor (for accessibility reasons)
        editor.commands.addCommand({
            name: 'LooseFocus',
            bindKey: {win: 'Esc', mac: 'Esc'},
            exec: () => {
                editor.blur();
            },
            readOnly: true
        });

        // sets the ace worker path, if running from concatenated
        // or minified source
        if (isDefined(opts.workerPath)) {
            const config = aceImport.require('ace/config');
            config.set('workerPath', opts.workerPath);
        }
        // ace requires loading
        if (isDefined(opts.require)) {
            opts.require.forEach(function (n) {
                aceImport.require(n);
            });
        }
        // Boolean options
        if (isDefined(opts.showGutter)) {
            editor.renderer.setShowGutter(opts.showGutter);
        }
        if (isDefined(opts.useWrapMode)) {
            session.setUseWrapMode(opts.useWrapMode);
        }
        if (isDefined(opts.showInvisibles)) {
            editor.renderer.setShowInvisibles(opts.showInvisibles);
        }
        if (isDefined(opts.showIndentGuides)) {
            editor.renderer.setDisplayIndentGuides(opts.showIndentGuides);
        }
        if (isDefined(opts.useSoftTabs)) {
            session.setUseSoftTabs(opts.useSoftTabs);
        }
        if (isDefined(opts.showPrintMargin)) {
            editor.setShowPrintMargin(opts.showPrintMargin);
        }
        if (isDefined(opts.readOnly)) {
            editor.setReadOnly(opts.readOnly);
        }
        // commands
        if (isDefined(opts.disableSearch) && opts.disableSearch) {
            editor.commands.addCommands([
                {
                    name: 'unfind',
                    bindKey: {
                        win: 'Ctrl-F',
                        mac: 'Command-F'
                    },
                    exec() {
                        return false;
                    },
                    readOnly: true
                }
            ]);
        }

        // Basic options
        if (isDefined(opts.theme)) {
            editor.setTheme(`ace/theme/${opts.theme}`);
        }
        if (isDefined(opts.mode)) {
            session.setMode(`ace/mode/${opts.mode}`);
        }

        // Advanced options
        if (isDefined(opts.placeholder)) {
            editor.setOption('placeholder', opts.placeholder)
        }
        if (isDefined(opts.firstLineNumber) && Number.isInteger(opts.firstLineNumber)) {
            session.setOption('firstLineNumber', opts.firstLineNumber);
        }

        // advanced options
        let key;
        let obj;
        if (isDefined(opts.advanced)) {
            for (key in opts.advanced) {
                if (Object.hasOwn(opts.advanced, key)) {
                    // create a javascript object with the key and value
                    obj = {name: key, value: opts.advanced[key]};
                    // try to assign the option to the ace editor
                    editor.setOption(obj.name, obj.value);
                }
            }
        }

        // advanced options for the renderer
        if (isDefined(opts.rendererOptions)) {
            for (key in opts.rendererOptions) {
                if (Object.hasOwn(opts.rendererOptions, key)) {
                    // create a javascript object with the key and value
                    obj = {name: key, value: opts.rendererOptions[key]};
                    // try to assign the option to the ace editor
                    editor.renderer.setOption(obj.name, obj.value);
                }
            }
        }

        // Custom onLoad function call from ACE options
        if (isDefined(opts.onLoad) && isFunction(opts.onLoad)) {
            opts.onLoad(editor);
        }
    }

    // FORM CONTROLS

    const RESET_SYMBOL = Symbol('reset-notification');

    function extractCurrentViewValue(): string {
        return session.getValue();
    }

    function renderViewValue(viewValue: string) {
        // do not trim editors value otherwise empty line would cause trimmed view value to overwrite current value and set cursor to the start
        if (session.getValue() !== viewValue) {
            session.setValue(viewValue ?? '');
            resetNotification$.next(RESET_SYMBOL);
        }
    }

    function createInputValueStreamFromEvent(eventName: string, debounce: number): Observable<string> {
        return fromEvent(inputElement, eventName).pipe(map(() => extractCurrentViewValue()), debounceTime(debounce))
    }

    const inputValueStreams = formControl.formControlOptions.updateTriggers.map(({
                                                                                     eventName,
                                                                                     debounceValue = 0
                                                                                 }) => createInputValueStreamFromEvent(`ace-${eventName}`, debounceValue));

    const resetNotification$ = new Subject();

    const inputValuesSubscription = merge(...inputValueStreams, resetNotification$)
        .pipe(
            distinctUntilChanged(),
            filter((value) => value !== RESET_SYMBOL),
            filter((value) => value === extractCurrentViewValue())) // ignore debounced value if model was changed in the meantime from outside
        .subscribe((value) => formControl.setViewValue(value));

    const viewValueSubscription = formControl.getViewValue$().subscribe((viewValue) => renderViewValue(viewValue));

    return {
        update: (updatedAceControls: AceFormControls) => setOptions(updatedAceControls.options),
        destroy: () => {
            resizeObserver.disconnect();
            editor.removeListener('change', onChange);
            editor.removeListener('blur', onBlur);

            editor.destroy();
            inputValuesSubscription.unsubscribe(); // event listeners and rxjs cleanup
            viewValueSubscription.unsubscribe();
        }
    }
}

interface AceFormControls {
    formControl: FormControl<string>;
    options: Record<string, any>;
}