<script lang="ts">
    import type {Fond} from 'typings/portaro.be.types';
    import type {ViewLayout} from 'shared/layouts/grid-system/built-layout/types';
    import type {SvelteComponentConstructor} from 'core/types';
    import {KpFondsService} from './services/kp-fonds.service';
    import {getInjector} from 'core/svelte-context/context';
    import {LayoutBuilderUtil} from 'shared/layouts/grid-system/built-layout/layout-builder-util';
    import {onMount} from 'svelte';
    import {createPageContext} from 'shared/layouts/page-context';
    import {defaultViewLayoutSettings} from 'shared/layouts/grid-system/built-layout/constants';
    import FondsHeadingPart from './parts/FondsHeadingPart.svelte';
    import FondsTablePart from './parts/FondsTablePart.svelte';
    import FondsDiagramPart from './parts/FondsDiagramPart.svelte';
    import KpLayoutBuilder from 'shared/layouts/grid-system/built-layout/KpLayoutBuilder.svelte';
    import KpLayoutGridContainer from 'shared/layouts/grid-system/KpLayoutGridContainer.svelte';

    const service = getInjector().getByToken<KpFondsService>(KpFondsService.serviceName);
    const pageContext = createPageContext<undefined, Fond[]>();

    const pageLayout: ViewLayout = {
        settings: defaultViewLayoutSettings,
        parts: [
            LayoutBuilderUtil.createInRowComponent('default', 'heading'),
            LayoutBuilderUtil.createInRowComponent('default', 'table'),
            LayoutBuilderUtil.createInRowComponent('wide', 'diagram')
        ]
    }

    const supportedFondsComponents: Record<string, SvelteComponentConstructor> = {
        heading: FondsHeadingPart,
        table: FondsTablePart,
        diagram: FondsDiagramPart
    }

    onMount(async () => {
        const loadedFonds = await service.getAllFonds();
        pageContext.updateReactiveData(loadedFonds);
    });
</script>

<KpLayoutGridContainer additionalClasses="kp-fonds-page" rowsWidth="{pageLayout.settings.defaultRowWidth}">
    <KpLayoutBuilder layout="{pageLayout}" supportedComponents="{supportedFondsComponents}"/>
</KpLayoutGridContainer>