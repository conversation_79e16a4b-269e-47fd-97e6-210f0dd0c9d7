import {Kind, SearchType} from 'shared/constants/portaro.constants';
import {createSearchManagerBackedPageableList} from '../../search/search-manager/search-manager';
import type {Rec, RecordOperation, SearchParams} from 'typings/portaro.be.types';
import type {PageableList} from 'shared/utils/reactive-pageable-list';
import type {EmptyObject} from 'typings/portaro.fe.types';
import type {SearchManagerBuilderFactoryService} from '../../search/search-manager/search-manager-factory.service';

export class AuthorityDetailTabsetService {
    public static serviceName = 'authorityDetailTabsetService';

    /*@ngInject*/
    constructor(private searchManagerBuilderFactoryService: SearchManagerBuilderFactoryService) {
    }

    public createOperationPageableList(record: Rec): PageableList<RecordOperation, string, EmptyObject> {
        const staticParams = {
            type: SearchType.TYPE_RECORD_OPERATION_SEARCH,
            kind: [Kind.KIND_RECORD_OPERATION],
            record: [record.id],
            pageSize: 10
        };
        const searchManager = this.searchManagerBuilderFactoryService
            .createBuilder<SearchParams, RecordOperation>()
            .withStaticParams(staticParams)
            .createLocalSearch();
        return createSearchManagerBackedPageableList(searchManager);
    }
}