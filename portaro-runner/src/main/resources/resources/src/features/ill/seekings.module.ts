import register from '@kpsys/angularjs-register';

import seekingsRoutes from './seekings.routes';
import SeekingDataService from './seeking.data-service';
import SeekingService from './seeking.service';

export default register('portaro.features.seekings')
    .config(seekingsRoutes)
    .service(SeekingDataService.serviceName, SeekingDataService)
    .service(SeekingService.serviceName, SeekingService)
    .name();