<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {encodeUri} from 'shared/utils/pipes';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpActionRequestButton from 'shared/components/kp-action-request-button/KpActionRequestButton.svelte';

    const localize = getLocalization();

    const coverTransferSetting = {folder: '', filenamePattern: ''};
</script>

<KpGenericPanel>
    <h2 slot="heading" class="unset-style">
        {localize(/* @kp-localization util.CoverImport */ 'util.CoverImport')}
    </h2>

    <form class="util-cover-import-container">
        <div class="form-group">
            <label>
                {localize(/* @kp-localization util.CoverImport.Directory */ 'util.CoverImport.Directory')}:
                <input type="text" class="form-control" bind:value={coverTransferSetting.folder}>
            </label>
        </div>

        <div class="form-group">
            <label>
                {localize(/* @kp-localization util.CoverImport.FilePattern */ 'util.CoverImport.FilePattern')}:
                <input type="text" class="form-control" bind:value={coverTransferSetting.filenamePattern}>
            </label>
        </div>

        <KpActionRequestButton isBlock
                               isDisabled="{!(coverTransferSetting.filenamePattern && coverTransferSetting.folder)}"
                               buttonStyle="brand-orange-new"
                               path="api/cover-transfer?folder={pipe(coverTransferSetting.folder, encodeUri())}}&filenamePattern={pipe(coverTransferSetting.filenamePattern ?? '', encodeUri())}">

            <IconedContent icon="move-to-folder">
                {localize(/* @kp-localization util.CoverImport.TransferCover */ 'util.CoverImport.TransferCover')}
            </IconedContent>
        </KpActionRequestButton>
    </form>
</KpGenericPanel>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    h2 {
        font-weight: 500;
    }

    .util-cover-import-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;

        .form-group,
        label,
        input {
            margin: 0;
        }

        label,
        input[type='text'] {
            width: 100%;
        }

        label {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;
        }
    }
</style>