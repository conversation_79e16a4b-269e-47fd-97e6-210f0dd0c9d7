package cz.kpsys.portaro.commons.object.repo;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GenericHookableSaver<E, RET> implements HookableSaver<E, RET> {

    @NonNull Saver<E, RET> delegate;
    @NonNull @NonFinal Function<E, E> preHook = entity -> entity;
    @NonNull List<Consumer<RET>> postSuccessHooks = new ArrayList<>();

    @Override
    public HookableSaver<E, RET> addPreHook(@NonNull Function<E, E> preHook) {
        this.preHook = preHook;
        return this;
    }

    @Override
    public HookableSaver<E, RET> addPostSuccessHook(@NonNull Consumer<RET> postSuccessHook) {
        this.postSuccessHooks.add(postSuccessHook);
        return this;
    }

    @Override
    public @NonNull RET save(@NonNull E object) {
        E prehookedObject = preHook.apply(object);
        RET saved = delegate.save(prehookedObject);
        for (Consumer<RET> hook : postSuccessHooks) {
            hook.accept(saved);
        }
        return saved;
    }
}
