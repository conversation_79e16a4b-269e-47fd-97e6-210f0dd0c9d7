import type {ViewableExemplar, Volume} from 'typings/portaro.be.types';
import type ExemplarFilter from '../exemplar-filter';

export interface IssueAwareVolume extends Volume {
    issues?: ViewableExemplar[];
}

export interface KpVolumesComponentCapabilities {
    canCreateAnyExemplar: boolean;
    canCreateAnyVolume: boolean;
    canEditAnyExemplar: boolean;
    canEditAnyVolume: boolean;
}

export interface KpVolumesInitializeData {
    exemplarFilter: ExemplarFilter;
    volumes: IssueAwareVolume[];
}