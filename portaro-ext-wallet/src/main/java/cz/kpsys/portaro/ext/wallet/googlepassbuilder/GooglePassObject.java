package cz.kpsys.portaro.ext.wallet.googlepassbuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;

import java.util.List;

@Builder
public record GooglePassObject(
        String id,
        String classId,
        String state,
        GPLocalizedString cardTitle,
        GPLocalizedString subheader,
        GPLocalizedString header,
        List<GPTextModule> textModulesData,
        GPBarcode barcode,
        String hexBackgroundColor,
        GPImage logo,
        GPImage heroImage
) {}