package cz.kpsys.portaro.search.restriction.convert;

import cz.kpsys.portaro.search.restriction.Not;
import cz.kpsys.portaro.search.restriction.Restriction;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class FieldTypeConvertingNotConverter<FIELD, TARGET> extends AbstractNotConverter<FIELD, Restriction<? extends TARGET>> {

    public FieldTypeConvertingNotConverter(@NonNull Converter<Restriction<? extends FIELD>, Restriction<? extends TARGET>> restrictionConverter) {
        super(restrictionConverter);
    }

    @Override
    protected Restriction<? extends TARGET> convertNot(Restriction<? extends TARGET> negatedValue) {
        return new Not<TARGET>(negatedValue);
    }
}
