package cz.kpsys.portaro.view;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class ViewableItemsConverter {

    public enum ViewMode {
        STANDARD, TABLE_ROW
    }

    @NonNull List<ConverterRegistration<?>> converters = new ArrayList<>();

    public <SOURCE_ITEM> ViewableItemsConverter register(@NonNull Class<SOURCE_ITEM> sourceItemClass,
                                                         @NonNull ViewMode viewMode,
                                                         @NonNull ViewableItemsTypedConverter<SOURCE_ITEM, ?> converter) {
        converters.add(new ConverterRegistration<>(sourceItemClass, viewMode, converter));
        return this;
    }

    public <SOURCE_ITEM> ViewableItemsConverter registerStandardNoop(@NonNull Class<SOURCE_ITEM> sourceItemClass) {
        return register(sourceItemClass, ViewMode.STANDARD, new NoopViewableItemsTypedConverter<>());
    }

    public List<?> convert(@NonNull List<?> sourceItems, @NonNull UserAuthentication currentAuth, Department ctx, Locale locale, @NonNull ViewMode viewMode, List<String> exports, Map<String, Object> additionalModel) {
        Set<IndexedItem<?>> indexedSourceItems = indexSourceItems(sourceItems);
        Set<IndexedItem<?>> indexedConvertedItems = new HashSet<>(sourceItems.size());

        Collection<ConverterForSupportedItems<?, ?>> convertersWithSupportedItems = indexedSourceItems.stream()
                .collect(Collectors.groupingBy(indexedSourceItem -> getConverterForItem(indexedSourceItem, viewMode)))
                .entrySet()
                .stream()
                .map(ViewableItemsConverter::createConverterForSupportedItems)
                .collect(Collectors.toUnmodifiableSet());

        for (ConverterForSupportedItems<?, ?> converterForSupportedItems : convertersWithSupportedItems) {
            Collection<? extends IndexedItem<?>> convertedGroupItems = converterForSupportedItems.convert(currentAuth, ctx, locale, exports, additionalModel);
            indexedConvertedItems.addAll(convertedGroupItems);
        }

        return indexedConvertedItems.stream()
                .sorted(Ordered.COMPARATOR)
                .map(IndexedItem::item)
                .toList();
    }

    private static Set<IndexedItem<?>> indexSourceItems(@NonNull List<?> sourceItems) {
        Set<IndexedItem<?>> indexedSourceItems = new HashSet<>(sourceItems.size());
        for (int i = 0; i < sourceItems.size(); i++) {
            indexedSourceItems.add(new IndexedItem<>(sourceItems.get(i), i));
        }
        return indexedSourceItems;
    }

    private <S> ViewableItemsTypedConverter<?, ?> getConverterForItem(@NonNull IndexedItem<S> sourceItem, @NonNull ViewMode viewMode) {
        return converters.stream()
                .filter(converterRegistration -> converterRegistration.matches(sourceItem.item, viewMode))
                .map(converterRegistration -> (ConverterRegistration<S>) converterRegistration)
                .map(ConverterRegistration::converter)
                .findFirst()
                .orElseThrow(() -> new UnsupportedOperationException("Searched item %s is not convertable by any of registered converters. Please register converter for [type=%s (or parent) & viewMode=%s]".formatted(sourceItem, sourceItem.getClass().getSimpleName(), viewMode)));
    }

    @NonNull
    private static <ITEM, TARGET_ITEM> ViewableItemsConverter.ConverterForSupportedItems<ITEM, TARGET_ITEM> createConverterForSupportedItems(Map.Entry converterForIndexedItems) {
        ViewableItemsTypedConverter<ITEM, TARGET_ITEM> key = (ViewableItemsTypedConverter<ITEM, TARGET_ITEM>) converterForIndexedItems.getKey();
        List<IndexedItem<ITEM>> value = (List<IndexedItem<ITEM>>) converterForIndexedItems.getValue();
        return new ConverterForSupportedItems<ITEM, TARGET_ITEM>(key, value);
    }

    public record ConverterRegistration<SOURCE_ITEM>(
            @NonNull Class<SOURCE_ITEM> sourceItemClass,
            @NonNull ViewMode viewMode,
            @NonNull ViewableItemsTypedConverter<SOURCE_ITEM, ?> converter
    ) {

        public boolean matches(@NonNull Object sourceItem, @NonNull ViewMode viewMode) {
            return sourceItemClass.isInstance(sourceItem) && viewMode == this.viewMode;
        }
    }

    private record ConverterForSupportedItems<SOURCE_ITEM, TARGET_ITEM>(
            @NonNull ViewableItemsTypedConverter<SOURCE_ITEM, TARGET_ITEM> converter,
            @NonNull List<IndexedItem<SOURCE_ITEM>> indexedSourceItems
    ) {

        public Set<IndexedItem<TARGET_ITEM>> convert(@NonNull UserAuthentication currentAuth, Department ctx, Locale locale, List<String> exports, Map<String, Object> additionalModel) {
            List<SOURCE_ITEM> sourceItems = ListUtil.convert(indexedSourceItems, IndexedItem::item);
            List<TARGET_ITEM> convertedItems = converter.convert(sourceItems, currentAuth, ctx, locale, exports, additionalModel);
            Assert.state(sourceItems.size() == convertedItems.size(), () -> "Size of converted search items (%s) is not same as original (%s)".formatted(convertedItems.size(), sourceItems.size()));
            return indexTargetItemsAccordingToSourceItems(convertedItems);
        }

        @NonNull
        private Set<IndexedItem<TARGET_ITEM>> indexTargetItemsAccordingToSourceItems(List<TARGET_ITEM> convertedItems) {
            Set<IndexedItem<TARGET_ITEM>> indexedConvertedItems = new HashSet<>(convertedItems.size());
            for (int i = 0; i < convertedItems.size(); i++) {
                IndexedItem<SOURCE_ITEM> indexedSourceItem = indexedSourceItems.get(i);
                TARGET_ITEM convertedItem = convertedItems.get(i);
                indexedConvertedItems.add(new IndexedItem<>(convertedItem, indexedSourceItem.order()));
            }
            return indexedConvertedItems;
        }

    }

    private record IndexedItem<SOURCE_ITEM>(
            @NonNull SOURCE_ITEM item,
            @NonNull Integer order
    ) implements NonNullOrderedRecord {}

}
