package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseValueException;
import cz.kpsys.portaro.sip2.client.deserialize.RequiredSip2ValueParsing;
import cz.kpsys.portaro.sip2.client.deserialize.Sip2ResponseParser;
import cz.kpsys.portaro.sip2.client.model.Sip2CheckoutResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class Sip2CheckoutResponseParser implements Sip2ResponseParser<Sip2CheckoutResponse> {

    @NonNull FeeTypeParser feeTypeParser;
    @NonNull CurrencyTypeParser currencyTypeParser;
    @NonNull MediaTypeParser mediaTypeParser;

    public static Sip2CheckoutResponseParser createDefault() {
        return new Sip2CheckoutResponseParser(
                new FeeTypeParser(),
                new CurrencyTypeParser(),
                new MediaTypeParser()
        );
    }

    @Override
    public Sip2CheckoutResponse parse(RequiredSip2ValueParsing parsing) throws InvalidSip2ResponseValueException {
        Sip2CheckoutResponse response = new Sip2CheckoutResponse();

        response.setOk(parsing.from(2).first1Char().as01Boolean());
        response.setRenewalOk(parsing.from(3).first1Char().asNYBoolean());
        response.setMagneticMedia(parsing.from(4).first1Char().asNYUThroolean());
        response.setDesensitize(parsing.from(5).first1Char().asNYUThroolean());
        response.setTransactionDate(parsing.from(6).dateTimeInstant());

        RequiredSip2ValueParsing fieldsParsing = parsing.from(24);

        response.setInstitutionId(fieldsParsing.field(AO_INSTITUTION_ID).stringValue());
        response.setPatronIdentifier(fieldsParsing.field(AA_PATRON_IDENTIFIER).stringValue());
        response.setItemIdentifier(fieldsParsing.field(AB_ITEM_IDENTIFIER).stringValue());
        response.setTitleIdentifier(fieldsParsing.field(AJ_TITLE_IDENTIFIER).stringValue());
        response.setDueDate(fieldsParsing.field(AH_DUE_DATE).stringValue());

        response.setFeeType(fieldsParsing.field(BT_FEE_TYPE).optional().map(p -> feeTypeParser.getFeeType(p.numericStringValue())));
        response.setSecurityInhibit(fieldsParsing.field(CI_SECURITY_INHIBIT).optional().asNYMissingThroolean());
        response.setCurrencyType(fieldsParsing.field(BH_CURRENCY_TYPE).optional().map(currencyTypeParser::getCurrencyType));
        response.setFeeAmount(fieldsParsing.field(BV_FEE_AMOUNT).optional().notBlank().stringValue());
        response.setMediaType(fieldsParsing.field(CK_MEDIA_TYPE).optional().map(mediaTypeParser::getMediaType));
        response.setItemProperties(fieldsParsing.field(CH_ITEM_PROPERTIES).optional().notBlank().stringValue());
        response.setTransactionId(fieldsParsing.field(BK_TRANSACTION_ID).optional().notBlank().stringValue());

        response.setScreenMessage(fieldsParsing.fieldValues(AF_SCREEN_MESSAGE));
        response.setPrintLine(fieldsParsing.fieldValues(AG_PRINT_LINE));

        fieldsParsing.sequence().ifPresent(response::setSequence);

        return response;
    }
}
