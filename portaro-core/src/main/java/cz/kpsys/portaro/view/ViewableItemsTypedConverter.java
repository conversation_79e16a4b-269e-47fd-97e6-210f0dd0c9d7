package cz.kpsys.portaro.view;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Locale;
import java.util.Map;

import static cz.kpsys.portaro.commons.util.ListUtil.firstOrEmptyOptional;

public interface ViewableItemsTypedConverter<SOURCE_ITEM, TARGET_ITEM> extends ViewableObjectTypedConverter<List<SOURCE_ITEM>, Department, List<TARGET_ITEM>> {

    @NonNull
    List<TARGET_ITEM> convert(@NonNull List<SOURCE_ITEM> sourceItems, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exports, @NonNull Map<String, Object> additionalModel);

    @NonNull
    default List<TARGET_ITEM> convert(@NonNull List<SOURCE_ITEM> sourceItems, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale) {
        return convert(sourceItems, currentAuth, ctx, locale, List.of(), Map.of());
    }

    @NonNull
    default TARGET_ITEM convertSingle(@NonNull SOURCE_ITEM sourceItem, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exports, @NonNull Map<String, Object> additionalModel) {
        Converter<List<SOURCE_ITEM>, List<TARGET_ITEM>> listConverter = sourceItems -> convert(sourceItems, currentAuth, ctx, locale, exports, additionalModel);
        List<TARGET_ITEM> converted = listConverter.convert(List.of(sourceItem));
        return firstOrEmptyOptional(converted).orElseThrow();
    }

    @NonNull
    default TARGET_ITEM convertSingle(@NonNull SOURCE_ITEM sourceItem, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale) {
        return convertSingle(sourceItem, currentAuth, ctx, locale, List.of(), Map.of());
    }

}
