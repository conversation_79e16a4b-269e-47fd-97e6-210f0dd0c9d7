package cz.kpsys.portaro.sip2.server.impl.handler;


import cz.kpsys.portaro.loan.Loan;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import static cz.kpsys.portaro.sip2.server.impl.Sip2ServerImplConstants.ExemplarIdentifierPrefix.DELIMITER;
import static cz.kpsys.portaro.sip2.server.impl.Sip2ServerImplConstants.ExemplarIdentifierPrefix.LOAN_ID;

public class Sip2LoanToItemIdentifierConverter implements Converter<@NonNull Loan, @NonNull String> {

    @NonNull
    @Override
    public String convert(@NonNull Loan source) {
        if (source.getExemplar() == null || source.getDesignation() == null) { // E.g. loans with not-already assigned exemplars (reservations, ..) or exernal loans
            return LOAN_ID + DELIMITER + source.getLoanRealizationId();
        }
        return source.getDesignation();
    }
}
