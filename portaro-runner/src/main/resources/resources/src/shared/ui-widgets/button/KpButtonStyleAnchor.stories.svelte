<script>
    import {Meta, Template, Story} from '@storybook/addon-svelte-csf';
    import KpButtonStyleAnchor from './KpButtonStyleAnchor.svelte';
</script>

<Meta title="Widgets/KpButtonStyleAnchor"
      description="anchor (link tag) styled as button"
      component={KpButtonStyleAnchor}

      argTypes={{
            buttonStyle: {
                control: { type: 'select' },
                options: ['default' , 'primary' , 'success' , 'info' , 'warning' , 'danger', 'link']
            },
            buttonSize: {
                control: { type: 'select' },
                options: ['xs' , 'sm' , 'md' , 'lg'],
            },
            isBlock: { control: 'boolean' },
            isDisabled: { control: 'boolean' },
            additionalClasses: { control: 'text' },
            id: { control: 'text' },
            slot: { control: 'text' }
  }}
/>

<Template let:args>
    <KpButtonStyleAnchor href="javascript:void(0)" {...args}>{args.slot}</KpButtonStyleAnchor>
</Template>

<Story name='Default'
       args={{slot: 'hello', buttonStyle: 'default',  buttonSize: 'md'}}
/>