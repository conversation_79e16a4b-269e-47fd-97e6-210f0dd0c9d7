package cz.kpsys.portaro.stats.data;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.stats.model.LabeledCount;
import cz.kpsys.portaro.stats.model.MultipleCountsStat;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CountsStatResultSetExtractor implements ResultSetExtractor<MultipleCountsStat<Long>> {

    @NonNull String countColumn;
    @NonNull String nameColumn;
    
    @Override
    public MultipleCountsStat<Long> extractData(ResultSet rs) throws SQLException, DataAccessException {
        List<LabeledCount<Long>> counts = new ArrayList<>();
        while (rs.next()) {
            counts.add(LabeledCount.ofLong(
                    Texts.ofNative(rs.getString(nameColumn)),
                    rs.getLong(countColumn)
            ));
        }
        return MultipleCountsStat.ofLongs(counts);
    }
    
}
