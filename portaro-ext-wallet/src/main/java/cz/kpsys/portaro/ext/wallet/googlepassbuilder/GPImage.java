package cz.kpsys.portaro.ext.wallet.googlepassbuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public record GPImage(
        GPSourceUri sourceUri,
        GPLocalizedString contentDescription
) {

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record GPSourceUri(
            String uri
    ) {}
}
