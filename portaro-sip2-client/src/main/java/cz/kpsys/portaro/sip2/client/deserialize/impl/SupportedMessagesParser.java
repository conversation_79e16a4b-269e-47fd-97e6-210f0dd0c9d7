package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.sip2.client.deserialize.RequiredSip2ValueParsing;
import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseException;
import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseValueException;
import cz.kpsys.portaro.sip2.client.model.SupportedMessages;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SupportedMessagesParser {

    public SupportedMessages parse(RequiredSip2ValueParsing parsing) throws InvalidSip2ResponseValueException, InvalidSip2ResponseException {
        SupportedMessages messages = new SupportedMessages();
        messages.setPatronStatusRequest(parsing.from(0).first1Char().asNYBoolean());
        messages.setCheckout(parsing.from(1).first1Char().asNYBoolean());
        messages.setCheckin(parsing.from(2).first1Char().asNYBoolean());
        messages.setBlockPatron(parsing.from(3).first1Char().asNYBoolean());
        messages.setScilSStatus(parsing.from(4).first1Char().asNYBoolean());
        messages.setRequestSCILSResend(parsing.from(5).first1Char().asNYBoolean());
        messages.setLogin(parsing.from(6).first1Char().asNYBoolean());
        messages.setPatronInformation(parsing.from(7).first1Char().asNYBoolean());
        messages.setEndPatronSession(parsing.from(8).first1Char().asNYBoolean());
        messages.setFeePaid(parsing.from(9).first1Char().asNYBoolean());
        messages.setItemInformation(parsing.from(10).first1Char().asNYBoolean());
        messages.setItemStatusUpdate(parsing.from(11).first1Char().asNYBoolean());
        messages.setPatronEnable(parsing.from(12).first1Char().asNYBoolean());
        messages.setHold(parsing.from(13).first1Char().asNYBoolean());
        messages.setRenew(parsing.from(14).first1Char().asNYBoolean());
        messages.setRenewAll(parsing.from(15).first1Char().asNYBoolean());
        return messages;
    }
}
