<script lang="ts">
    import type {Writable} from 'svelte/store';
    import type {HeadlessTabsApiData} from 'shared/ui-widgets/tabset/types';
    import {TABS_CONTEXT_NAME} from 'shared/ui-widgets/tabset/types';
    import {TabPanels} from '@rgossiaux/svelte-headlessui';
    import {fade} from 'svelte/transition';
    import {getContext, onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';

    const api: Writable<any> = getContext(TABS_CONTEXT_NAME);
    if (!api) {
        throw new Error(
            '<KpTabGroupActiveTabSelector /> is missing a parent <TabGroup /> component.'
        );
    }

    let displayingNoneTab = false;

    const apiUnsubscribe = api.subscribe((apiData: HeadlessTabsApiData) => {
        if (apiData.tabs.length === 0) {
            return;
        }

        const selectedTab = apiData.tabs[apiData.selectedIndex];
        displayingNoneTab = exists(selectedTab) && selectedTab.classList.contains('tabset-item-hidden');
    });

    onDestroy(() => {
        apiUnsubscribe();
    });
</script>

<TabPanels class="kp-generic-tab-panels {displayingNoneTab ? 'displaying-none-tab' : ''}" let:selectedIndex>
    {#key selectedIndex}
        <div in:fade={{duration: 250}}>
            <slot {selectedIndex}/>
        </div>
    {/key}
</TabPanels>

<style lang="less">
    :global {
        .kp-generic-tab-panels {
            margin-top: 8px;

            &.displaying-none-tab,
            &:empty {
                display: none;
            }
        }
    }
</style>