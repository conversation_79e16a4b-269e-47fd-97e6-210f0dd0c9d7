package cz.kpsys.portaro.oai.dc;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import cz.kpsys.portaro.oai.model.MetadataContainer;
import lombok.NonNull;

public record RecordDcMetadataResponse(

        @JacksonXmlProperty(localName = "dc", namespace = "http://www.openarchives.org/OAI/2.0/oai_dc/")
        @NonNull
        RecordDcDto dc

) implements MetadataContainer {}
