package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LabeledId<ID extends Serializable> extends BasicIdentified<ID> implements Serializable, Identified<ID>, LabeledIdentified<ID> {

    @Getter
    @NonNull
    Text text;

    public LabeledId() {
        this(null, Texts.ofEmpty());
    }

    public LabeledId(ID id) {
        this(id, id == null ? Texts.ofEmpty() : Texts.ofNative(String.valueOf(id)));
    }

    public LabeledId(ID id, @Nullable Text text) {
        super(id);
        this.text = ObjectUtil.firstNotNull(text, Texts.ofEmpty());
    }

    public static <ID extends Serializable> LabeledId<ID> createWithIdAsNativeText(ID id) {
        return new LabeledId<>(id, Texts.ofNative(String.valueOf(id)));
    }


    public static <E> Constructor<E> getIdAndStringParameterConstructor(Class<E> clazz) {
        return findIdAndSomeParameterConstructor(clazz, String.class)
                .orElseThrow(() -> new IllegalStateException(String.format("Class %s has no or no usable constructor (with 2 arguments and 2. arg is String)", clazz)));
    }

    public static <E> Optional<Constructor<E>> findIdAndSomeParameterConstructor(Class<E> clazz, Class<?> secondParamClass) {
        if (clazz.getConstructors().length == 0) {
            return Optional.empty();
        }
        return Arrays.stream(clazz.getDeclaredConstructors())
                .filter(constructor -> constructor.getParameterTypes().length == 2 && constructor.getParameterTypes()[1].equals(secondParamClass))
                .map(constructor -> (Constructor<E>) constructor)
                .findFirst();
    }

}
