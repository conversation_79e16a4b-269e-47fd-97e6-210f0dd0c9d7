package cz.kpsys.portaro.commons.validation;

import jakarta.validation.ConstraintValidatorContext;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldValidatorHoldingFieldValidatorResult implements FieldValidatorResult {

    @NonNull
    FieldValidator validator;

    @Getter
    @NonNull
    Set<String> invalidFields;

    public FieldValidatorHoldingFieldValidatorResult setFieldFailedValidationToContextWithMessage(ConstraintValidatorContext context) {
        invalidFields.forEach(requiredField -> {
            String invalidMessage = validator.getInvalidMessage(requiredField);
            ValidationUtils.setFieldFailedValidationToContextWithMessage(requiredField, context, invalidMessage);
        });
        return this;
    }
}
