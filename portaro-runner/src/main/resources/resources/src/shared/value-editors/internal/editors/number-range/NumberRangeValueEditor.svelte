<script lang="ts">
    import type {FormControlOptions} from '../../forms/types';
    import type {EditorsLocalizationFunction} from '../../../localizations/types';
    import type {RangeValueEditorModel} from '../range/types';
    import type {NumberRangeValueEditorLocalizations, NumberRangeValueEditorModel, NumberRangeValueEditorValidations} from './types';
    import type {ValueEditorOptions} from '../../../kp-value-editor/types';
    import type {FormGroup} from '../../forms/form-group';
    import type {ValueEditorSize} from '../../../types';
    import {FormControlBuilder} from '../../forms/form-control';
    import {formControls} from '../../forms/use.form-controls';
    import {getContext} from 'svelte';
    import {FormGroupBuilder} from '../../forms/form-group';
    import {onDestroy} from 'svelte';
    import {throwParseErrorIfNaN, validatorFactory} from './utils';
    import {focusOnMount} from '../../shared/use.focus-on-mount';
    import {touched} from '../../shared/use.touched';
    import {ariaInvalid} from '../../errors/use.aria-invalid';
    import {ariaErrormessage} from '../../errors/use.aria-errormessage';
    import {style} from 'svelte-forms';
    import {emptyAsNullParser} from '../_shared/editors-utils';
    import {createInputDescription} from '../../description/utils';
    import {ariaDescribedby} from '../../description/use.aria-describedby';
    import {required} from '../../forms/use.required';
    import {getLogger} from 'core/svelte-context/context';
    import {ariaLabelledby} from '../../label/use.aria-labelledby';
    import {getValueEditorLabelContext} from '../../label/value-editor-label-context';
    import {EDITORS_LOCALIZE} from '../../../context-keys';
    import ErrorMessages from '../../errors/ErrorMessages.svelte';
    import Description from '../../description/Description.svelte';

    export let model: NumberRangeValueEditorModel;
    export let formControlOptions: FormControlOptions;

    export let editorId: string;
    export let editorName: string;
    export let placeholder: string;

    export let isDisabled: boolean;
    export let isFocused: boolean;
    export let forceShowErrors: boolean;
    export let size: ValueEditorSize;

    export let options: ValueEditorOptions;
    export let validations: NumberRangeValueEditorValidations;

    const logger = getLogger();
    const localize: EditorsLocalizationFunction<NumberRangeValueEditorLocalizations> = getContext(EDITORS_LOCALIZE);
    const labelContext = getValueEditorLabelContext();

    let fromModel = model?.from as any;
    let toModel = model?.to as any;

    const formControlFrom = FormControlBuilder.for(`${editorId}-from`, `${editorName}-from`, fromModel)
        .withParsers([throwParseErrorIfNaN])
        .withOptions(formControlOptions)
        .build(logger);

    const subscriptionFrom = formControlFrom.getModelValue$().subscribe((value) => fromModel = value);  // two-way data binding -> sending updated value up to a parent
    $: formControlFrom.setModelValue(fromModel); // two-way data binding -> receiving new model value from parent

    const formControlTo = FormControlBuilder.for(`${editorId}-to`, `${editorName}-to`, toModel)
        .withParsers([throwParseErrorIfNaN])
        .withOptions(formControlOptions)
        .build(logger);

    const subscriptionTo = formControlTo.getModelValue$().subscribe((value) => toModel = value);  // two-way data binding -> sending updated value up to a parent
    $: formControlTo.setModelValue(toModel); // two-way data binding -> receiving new model value from parent

    const formGroupBuilder = new FormGroupBuilder<NumberRangeValueEditorModel>().addValidators(validatorFactory(editorName, validations)).addModifiers([emptyAsNullParser(options)]);
    formGroupBuilder.registerFormControl(formControlFrom);
    formGroupBuilder.registerFormControl(formControlTo);

    const formControl = formGroupBuilder.build(editorId, editorName, new Map(
        [[`${editorName}-from`, 'from'], [`${editorName}-to`, 'to']]
    ));

    const modelSubscription = formControl.getModelValue$().subscribe((value) => model = value);  // two-way data binding -> sending updated value up to a parent
    $: formControl.setModelValue(model); // two-way data binding -> receiving new model value from parent

    const inputDescription = createInputDescription({editorId, editorName, description: localize('description')});

    onDestroy(() => {
        modelSubscription.unsubscribe();
        subscriptionFrom.unsubscribe();
        subscriptionTo.unsubscribe();
    });

    export function getFormController(): FormGroup<RangeValueEditorModel> {
        return formControl;
    }
</script>

<div class="form-control multi-input-group" id={editorId} role="group" use:ariaLabelledby={labelContext} data-main-input>
    <div class="input-group from input-group-{size}">
        <label class="input-group-addon" for={editorId ? `${editorId}-from` : null}>{localize('from')}</label>
        <input class="form-control input-{size}"
               id={editorId ? `${editorId}-from` : null}
               name="{editorName}-from"
               type="number"
               min={validations.min}
               max={validations.max}
               use:required={validations.required}
               {placeholder}
               disabled={isDisabled}
               use:ariaErrormessage={formControl}
               use:ariaInvalid={formControl}
               use:focusOnMount={isFocused}
               use:formControls={formControlFrom}
               use:style={{field: formControl.getFieldStateAsStore()}}
               use:touched={{formControl:formControlFrom}}
               use:ariaDescribedby={inputDescription}
               data-input-from/>
    </div>

    <div class="input-group to input-group-{size}">
        <label class="input-group-addon" for={editorId ? `${editorId}-to` : null}>{localize('to')}</label>
        <input class="form-control input-{size}"
               id={editorId ? `${editorId}-to` : null}
               name="{editorName}-to"
               type="number"
               min={validations.min}
               max={validations.max}
               use:required={validations.required}
               {placeholder}
               disabled={isDisabled}
               use:ariaErrormessage={formControl}
               use:ariaInvalid={formControl}
               use:formControls={formControlTo}
               use:style={{field: formControl.getFieldStateAsStore()}}
               use:touched={{formControl:formControlTo}}
               use:ariaDescribedby={inputDescription}
               data-input-to/>
    </div>

</div>

<ErrorMessages formController={formControl} {forceShowErrors} {size}/>
<Description {inputDescription}/>

<style lang="less">
    .multi-input-group {
        padding: 0;
        border: 0;
        box-shadow: none;
        display: flex;
        align-items: baseline;

        .input-group {
            width: 50%;
            display: inline-table;

            label {
                font-weight: normal;
                margin: 0;
            }

            &.from {
                .form-control {
                    border-radius: 0;
                }
            }

            &.to {
                .input-group-addon {
                    border-radius: 0;
                    border-left: none;
                }
            }
        }
    }
</style>