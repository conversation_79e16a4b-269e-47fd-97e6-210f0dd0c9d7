package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class SignUpModal {

    final PageUtils pageUtils;

    @FindBy(css = "[data-qa='internal-login-new-registration-button']")
    WebElement newRegistrationButton;

    @FindBy(css = "input[data-qa='internal-login-username-input']")
    WebElement userNameInput;

    @FindBy(css = "input[data-qa='internal-login-password-input']")
    WebElement passwordInput;

    @FindBy(css = "button[data-qa='internal-login-submit-button']")
    WebElement submitButton;

    public SignUpModal(@NonNull WebDriver driver) {
        pageUtils = new PageUtils(driver);
        PageFactory.initElements(driver, this);
    }

    public SignUpModal clickOnNewRegistrationButton() {
        pageUtils.waitForElementAndClick(newRegistrationButton);
        return this;
    }

    public SignUpModal enterUserName(String userName) {
        pageUtils.waitForSteadinessAndSendKeys(userNameInput, userName);
        return this;
    }

    public SignUpModal enterPassword(String password) {
        pageUtils.waitForSteadinessAndSendKeys(passwordInput, password);
        return this;
    }

    public SignUpModal clickOnSubmitLoginButton() {
        pageUtils.waitForSteadinessAndClick(submitButton);
        return this;
    }
}
