package cz.kpsys.portaro.sql.generator;

import java.util.function.BiConsumer;
import java.util.function.Consumer;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;

public class SelectQueryUtils {

    public static void addKeywordsClause(String q, Brackets fieldsBracket, String[] columnNames) {
        String[] keywords = q.split(" ");
        for (String columnName : columnNames) {
            Brackets keywordsBracket = fieldsBracket.or().brackets();
            for (String keyword : keywords) {
                keywordsBracket.and().like(lower(columnName), keyword.toLowerCase(), true, true);
            }
        }
    }

    public static Query existsQuery(QueryFactory queryFactory, String sourceTable, String sourceColumn, String targetTable, String targetColumn) {
        return existsQueryWhere(queryFactory, sourceTable, sourceColumn, targetTable, targetColumn, null);
    }

    public static Query existsQueryWhere(QueryFactory queryFactory, String sourceTable, String sourceColumn, String targetTable, String targetColumn, Consumer<Brackets> additionalCondition) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(targetTable);
        sq.where().addStringCondition(COLSEQ(TC(targetTable, targetColumn), TC(sourceTable, sourceColumn)));
        if (additionalCondition != null) {
            additionalCondition.accept(sq.where());
        }
        return sq;
    }

    public static void selectWhereAndExistsQuery(SelectQuery sq, QueryFactory queryFactory, String sourceTable, String sourceColumn, String targetTable, String targetColumn, BiConsumer<Brackets, String> additionalCondition) {
        sq.where()
                .and()
                .exists(existsQueryWhere(queryFactory, sourceTable, sourceColumn, targetTable, targetColumn, brackets -> additionalCondition.accept(brackets, targetTable)));
    }

    public static void joinWithConditions(SelectQuery sq, String sourceTable, String sourceColumn, String targetTable, String targetColumn, String alias, BiConsumer<Brackets, String> additionalCondition) {
        sq.joins().add(AS(targetTable, alias), add -> {
            Brackets brackets = add.eqRaw(TC(sourceTable, sourceColumn), TC(alias, targetColumn));
            if (additionalCondition != null) {
                additionalCondition.accept(brackets, alias);
            }
        });
    }
}
