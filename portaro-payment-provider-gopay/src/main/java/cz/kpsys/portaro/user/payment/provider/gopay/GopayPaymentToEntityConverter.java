package cz.kpsys.portaro.user.payment.provider.gopay;

import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class GopayPaymentToEntityConverter implements Converter<GopayPayment, GopayPaymentEntity> {

    @Override
    public GopayPaymentEntity convert(@NonNull GopayPayment source) {
        return new GopayPaymentEntity(
                source.getId(),
                source.getGopayPaymentId()
        );
    }
}
