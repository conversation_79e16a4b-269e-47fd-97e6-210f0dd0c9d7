package cz.kpsys.portaro.ext.obalkyknih.metadata;

import cz.kpsys.portaro.commons.io.ExternalServiceException;
import cz.kpsys.portaro.commons.webclient.ExternalDownloader;
import cz.kpsys.portaro.ext.obalkyknih.web.*;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Arrays;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ObalkyknihDocumentMetadataLoader {

    public static final String API_PATH = "/api/books";

    @NonNull ExternalDownloader externalDownloader;
    @NonNull ObalkyknihRequestParamFactory obalkyknihRequestParamFactory;

    public @NonNull DocumentMetadata download(@NonNull Record document) throws MetadataException, ExternalServiceException {
        var params = obalkyknihRequestParamFactory.getQueryParams(document);
        if (params.isEmpty()) {
            throw new RecordIsNotSearchableException(document);
        }
        try {
            return doQueryMetadata(document, getMetadataUrl(params));
        } catch (NoDataException e) {
            return searchOneByOne(document, params);
        }
    }

    private DocumentMetadata doQueryMetadata(Record document, URI downloadUrl) throws MetadataException, ExternalServiceException {
        log.trace("Querying obalkyknih metadata for {} from: {}", document, downloadUrl);

        DocumentMetadata[] response = externalDownloader.download(downloadUrl.toString(), DocumentMetadata[].class);
        if (response.length == 0) {
            throw new NoDataException(document, downloadUrl, response);
        }
        Assert.state(response.length == 1, () -> String.format("Call should have returned only one item but returned %s! Document: %s, url: %s", Arrays.toString(response), document, downloadUrl));

        DocumentMetadata result = response[0];
        if (result.isNotIndexed()) {
            throw new NoDataException(document, downloadUrl, response);
        }
        return result;
    }

    private DocumentMetadata searchOneByOne(Record document, MultiQuery multiParams) throws MetadataException, ExternalServiceException {
        DocumentMetadata lastMetadata = null;
        for (SingleQuery singleParam : multiParams.expand()) {
            try {
                return doQueryMetadata(document, getMetadataUrl(singleParam));
            } catch (NoDataException e) {
                // Continue searching
            }
        }
        throw new NoDataException(document, getMetadataUrl(multiParams), lastMetadata);
    }

    private URI getMetadataUrl(Query params) {
        UriComponents uriComponents = UriComponentsBuilder.fromUriString(ObalkyknihConstants.BASE_URL)
                .path(API_PATH)
                .queryParam("multi", obalkyknihRequestParamFactory.metadataQueryParam(params))
                .build().encode(); // Šaškárna, aby se správně escapovaly [], {} a další znaky
        return uriComponents.toUri();
    }

}
