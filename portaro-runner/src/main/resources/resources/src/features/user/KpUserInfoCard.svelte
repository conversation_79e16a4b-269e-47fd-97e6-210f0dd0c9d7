<script lang="ts">
    import type {User} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {characters, inline, loc, prop} from 'shared/utils/pipes';
    import {isReaderBlocked, isReadersRegistrationExpired, isRegistrationNonExisting} from 'shared/utils/user-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import KpUsernameLabel from './KpUsernameLabel.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';

    export let user: User;

    const localize = getLocalization();
</script>

<div class="header">
    <slot userData={user}>
        <!-- slot for customizing heading level (default has no heading) -->
        <strong>
            <Label labeled={user} explicitKind={Kind.KIND_USER} additionalClasses="user-printable-name"/>
            {#if user.username}
                <KpUsernameLabel {user}/>
            {/if}
        </strong>
    </slot>

    {#if isReaderBlocked(user)}
        <span class="label label-danger">{localize(/* @kp-localization ctenar.TransactionsBlocked */ 'ctenar.TransactionsBlocked')}</span>
    {/if}
    {#if isReadersRegistrationExpired(user)}
        <span class="label label-danger">{localize(/* @kp-localization ctenar.RegistrationExpired */ 'ctenar.RegistrationExpired')}</span>
    {/if}
    {#if isRegistrationNonExisting(user)}
        <span class="label label-danger">{localize(/* @kp-localization ctenar.RegistrationIncomplete */ 'ctenar.RegistrationIncomplete')}</span>
    {/if}
    {#if user.role?.includes('ROLE_READER')}
        <span class="label label-info reader-tag">{localize(/* @kp-localization user.Reader */ 'user.Reader')}</span>
    {/if}
    {#if user.role?.includes('ROLE_LIBRARIAN')}
        <span class="label label-info reader-tag">{localize(/* @kp-localization user.Librarian */ 'user.Librarian')}</span>
    {/if}
    {#if user.role?.includes('ROLE_LIBRARY')}
        <span class="label label-info">{localize(/* @kp-localization user.MvsLibrary */ 'user.MvsLibrary')}</span>
    {/if}
    {#if user.role?.includes('ROLE_SUPPLIER')}
        <span class="label label-info">{localize(/* @kp-localization user.Supplier */ 'user.Supplier')}</span>
    {/if}
</div>

<div class="detail">
    <div>
        {#each user.readerAccounts ?? [] as readerAccount}
            {#if readerAccount.cardNumber}
                <span class="text-muted">{localize(/* @kp-localization ctenar.cardNumber */ 'ctenar.cardNumber')}</span>
                {readerAccount.cardNumber},
            {/if}

            {#if readerAccount.barCode}
                <span class="text-muted">{localize(/* @kp-localization ctenar.barCode */ 'ctenar.barCode')}</span>
                {readerAccount.barCode},
            {/if}

            <span class="text-muted">{localize(/* @kp-localization commons.kategorie */ 'commons.kategorie')}</span>
            {pipe(readerAccount.readerCategory, loc())},
        {/each}

        {#each user.editorAccounts ?? [] as editorAccount}
            {#if editorAccount.editLevel.value > -1}
                <span class="text-muted">{localize(/* @kp-localization user.LibrarianEditLevel */ 'user.LibrarianEditLevel')}</span>
                {pipe(editorAccount.editLevel, loc())},
            {/if}

            {#if exists(editorAccount.group)}
                <span class="text-muted">{localize(/* @kp-localization user.UserGroup */ 'user.UserGroup')}</span>
                {pipe(editorAccount.group, loc())},
            {/if}

            {#if exists(editorAccount.validationCode)}
                <span class="text-muted">{localize(/* @kp-localization user.ValidationCode */ 'user.ValidationCode')}</span>
                {editorAccount.validationCode},
            {/if}

            {#if editorAccount.withServicePrivileges}
                <span>{localize(/* @kp-localization user.withServicePrivileges */ 'user.withServicePrivileges')}</span>
            {/if}
        {/each}
    </div>

    <div>
        {#if exists(user.readableDepartments) && Array.isArray(user.readableDepartments) && user.readableDepartments.length > 0}
            <span class="text-muted">{localize(/* @kp-localization user.ReadableDepartments */ 'user.ReadableDepartments')}</span>
            {pipe(user.readableDepartments, prop('text'), inline(', '), characters(100))}
        {/if}
    </div>
</div>