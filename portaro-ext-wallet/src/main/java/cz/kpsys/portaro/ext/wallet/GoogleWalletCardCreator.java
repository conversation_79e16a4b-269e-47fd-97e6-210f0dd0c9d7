package cz.kpsys.portaro.ext.wallet;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.wallet.googlepassbuilder.*;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.UserStringGenerator;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.io.InputStream;
import java.net.URI;
import java.security.interfaces.RSAPrivateKey;
import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GoogleWalletCardCreator implements WalletCardCreator<URI> {

    @NonNull ContextualProvider<Department, @NonNull CardSettings> cardSettingsContextualProvider;
    @NonNull ContextualProvider<Department, @NonNull Map<Locale, CardLocalisation>> cardLocalisationsContextualProvider;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull ObjectMapper objectMapper;

    private static final String SERVICE_ACCOUNT_FILE = "/wallet/google/service-account.json";

    private static final String ISSUER_ID = "3388000000022931298"; // e.g., "issuer.systemist"
    private static final String CLASS_SUFFIX = "verbis-library-card";

    @Override
    public URI generateWalletCard(Person person, Department ctx) throws Exception {
        CardSettings cardSettings = cardSettingsContextualProvider.getOn(ctx);
        CardLocalisation localisation = cardLocalisationsContextualProvider.getOn(ctx).get(Locale.ENGLISH);

        if (!cardSettings.enabled()) {
            throw new RuntimeException("Google Wallet card generation is not enabled");
        }

        ReaderRole readerRole = person.roleStreamOn(ReaderRole.class, ctx)
                .findFirst()
                .orElseThrow();

        String passId = UUID.randomUUID().toString();
        String barcodeValue = readerRole.getBarCode();

        // Load service account credentials
        InputStream credentialsStream = getClass().getResourceAsStream(SERVICE_ACCOUNT_FILE);
        GoogleCredentials credentials = GoogleCredentials.fromStream(credentialsStream);

        if (!(credentials instanceof ServiceAccountCredentials serviceAccountCredentials)) {
            throw new IllegalStateException("Credentials are not a service account");
        }

        RSAPrivateKey privateKey = (RSAPrivateKey) serviceAccountCredentials.getPrivateKey();
        String serviceAccountEmail = serviceAccountCredentials.getClientEmail();

        List<GPTextModule> textModulesData = new ArrayList<>();

        if (readerRole.getRegistrationDate() != null) {
            textModulesData.add(GPTextModule.builder()
                    .id("registrationDate")
                    .header("Datum registrace")
                    .body(readerRole.getRegistrationDate().format(LOCAL_DATE_FORMATTER))
                    .build());
        }

        if (readerRole.getRegistrationExpirationDate().isPresent()) {
            textModulesData.add(GPTextModule.builder()
                    .id("expirationDate")
                    .header("Datum expirace")
                    .body(readerRole.getRegistrationExpirationDate().get().format(LOCAL_DATE_FORMATTER))
                    .build());
        }

        if (readerRole.getReaderCategory() != null) {
            textModulesData.add(GPTextModule.builder()
                    .id("category")
                    .header("Kategorie")
                    .body(readerRole.getReaderCategory().getName())
                    .build());
        }

        // Object
        GooglePassObject passObject = GooglePassObject.builder()
                .id(passId)
                .classId(ISSUER_ID + "." + CLASS_SUFFIX)
                .state("ACTIVE")
                .cardTitle(GPLocalizedString.createDefault("Testovací knihovna eVerbis"))
                .subheader(GPLocalizedString.createDefault("Čtenář"))
                .header(GPLocalizedString.createDefault(prettyUserNameGenerator.generate(person)))
                .textModulesData(textModulesData)
                .hexBackgroundColor(cardSettings.backgroundColor())
                .barcode(GPBarcode.builder()
                        .type("CODE_128")
                        .value(barcodeValue)
                        .build())
                .build();

        // JWT payload - use Jackson to serialize objects
        HashMap<String, Object> payload = new HashMap<>();
        // payload.put("genericClasses", List.of(passClass));
        payload.put("genericObjects", List.of(objectMapper.convertValue(passObject, Map.class)));

        Algorithm algorithm = Algorithm.RSA256(null, privateKey);
        String token = JWT.create()
                .withClaim("iss", serviceAccountEmail)
                .withClaim("aud", "google")
                .withClaim("typ", "savetowallet")
                .withClaim("payload", payload)
                .sign(algorithm);

        // Return Add to Google Wallet URL
        return URI.create("https://pay.google.com/gp/v/save/" + token);
    }
}
