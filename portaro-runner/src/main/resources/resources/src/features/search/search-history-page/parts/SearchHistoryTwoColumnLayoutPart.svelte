<script lang="ts">
    import type {SearchHistoryEntryDto} from 'typings/portaro.be.types';
    import type {ViewLayout} from 'shared/layouts/grid-system/built-layout/types';
    import {createPageContext} from 'shared/layouts/page-context';
    import {LayoutBuilderUtil} from 'shared/layouts/grid-system/built-layout/layout-builder-util';
    import {defaultViewLayoutSettings} from 'shared/layouts/grid-system/built-layout/constants';
    import KpLayoutBuilder from 'shared/layouts/grid-system/built-layout/KpLayoutBuilder.svelte';
    import SearchHistoryTablePart from './SearchHistoryTablePart.svelte';
    import KpFavouritesPanel from '../../../favourites/kp-favourites-panel/KpFavouritesPanel.svelte';
    import KpVisitedPanel from '../../../record/kp-visited-documents-panel/KpVisitedPanel.svelte';

    export let searchHistory: SearchHistoryEntryDto[];

    const searchHistoryLayout: ViewLayout = {
        settings: defaultViewLayoutSettings,
        parts: [
            LayoutBuilderUtil.createRow('default', [2, 1], [
                LayoutBuilderUtil.createColumn(false, [
                    LayoutBuilderUtil.createComponent('table')
                ]),
                LayoutBuilderUtil.createColumn(false, [
                    LayoutBuilderUtil.createComponent('favouritesPanel'),
                    LayoutBuilderUtil.createComponent('visitedPanel')
                ])
            ])
        ]
    };

    const searchHistoryComponents: Record<string, any> = {
        table: SearchHistoryTablePart,
        favouritesPanel: KpFavouritesPanel,
        visitedPanel: KpVisitedPanel
    };

    createPageContext<SearchHistoryEntryDto[]>(searchHistory);
</script>

<KpLayoutBuilder layout="{searchHistoryLayout}" supportedComponents="{searchHistoryComponents}"/>