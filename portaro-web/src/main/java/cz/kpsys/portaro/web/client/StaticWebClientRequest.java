package cz.kpsys.portaro.web.client;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.ip.IpAddress;
import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.BasicUser;
import lombok.*;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@Value
@With
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class StaticWebClientRequest implements WebClientRequest {

    @EqualsAndHashCode.Include
    @ToString.Include
    @NonNull
    UUID id;

    @NonNull
    String sessionId;

    @ToString.Include
    @Nullable
    String httpSessionId;

    @NonNull
    Instant creationDate;

    @ToString.Include
    @NonNull
    IpAddress ipAddress;

    @Nullable
    String userAgent;

    @NonNull
    String url;

    @JsonIgnore
    @NonNull
    BasicUser initiatorUser;

    @NonNull
    Department currentDepartment;

    public Optional<String> getHttpSessionId() {
        return Optional.ofNullable(httpSessionId);
    }

    @Override
    public String toString() {
        return "WebClientRequest{" +
               "httpSessionId='" + httpSessionId + '\'' +
               ", ipAddress=" + ipAddress +
               ", url='" + url + '\'' +
               ", initiatorUser=" + initiatorUser.getId() +
               ", userAgent=" + userAgent +
               '}';
    }

    @JsonProperty("initiatorUser")
    public LabeledIdentified<?> getInitiatorUserJson() {
        return new LabeledId<>(initiatorUser.getId(), initiatorUser.getText());
    }
}
