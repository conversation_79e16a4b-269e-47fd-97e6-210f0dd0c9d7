package cz.kpsys.portaro.auth.ldap;

import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

import java.util.List;

public class LdapAuthSettingKeys {

    public static final String SECTION_AUTH_LDAP = "auth.ldap";

    public static final SettingKey<@NonNull Boolean> LDAP_ENABLED = new SettingKey<>(SECTION_AUTH_LDAP, "enabled");
    public static final SettingKey<String> LDAP_URL = new SettingKey<>(SECTION_AUTH_LDAP, "url");
    public static final SettingKey<String> LDAP_USER_SEARCH_FILTER_TEMPLATE = new SettingKey<>(SECTION_AUTH_LDAP, "userSearchFilterTemplate");
    public static final SettingKey<List<String>> LDAP_BASE_DNS = new SettingKey<>(SECTION_AUTH_LDAP, "baseDNs");
    public static final SettingKey<@NonNull Boolean> LDAP_CERTIFICATE_VERIFICATION_ENABLED = new SettingKey<>(SECTION_AUTH_LDAP, "certificateVerificationEnabled");
    public static final SettingKey<String> LDAP_CONTEXT_CONNECT_USER_DN = new SettingKey<>(SECTION_AUTH_LDAP, "contextConnectUserDN");
    public static final SettingKey<String> LDAP_CONTEXT_CONNECT_USER_PASSWORD = new SettingKey<>(SECTION_AUTH_LDAP, "contextConnectUserPassword");
}
