dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-search-factory"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-tx:6.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("org.apache.velocity:velocity-engine-core:2.+")
    implementation("org.apache.velocity.tools:velocity-tools-generic:3.+")
}
