package cz.kpsys.portaro.ext.wallet.googlepassbuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.NonNull;

public record GPLocalizedString(
        LangString defaultValue
) {

    public static GPLocalizedString createDefault(String value) {
        return new GPLocalizedString(
                new LangString("cs-CZ", value)
        );
    }

    public static GPLocalizedString createDefault(String language, String value) {
        return new GPLocalizedString(
                new LangString(language, value)
        );
    }

    public record LangString(
            @NonNull String language,
            @NonNull String value
    ) {}
}
