package cz.kpsys.portaro.user.payment.provider.csobgw;

import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.payment.Payment;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.SQLException;

import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI_CSOBGW.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CsobGwPaymentRowMapper implements RowMapper<CsobGwPayment> {

    @NonNull RowMapper<Payment> paymentRowMapper;

    @Override
    @Transactional(readOnly = true)
    public CsobGwPayment mapRow(ResultSet rs, int rowNum) throws SQLException {
        Payment payment = paymentRowMapper.mapRow(rs, rowNum);

        String orderNumber = rs.getString(CSOBGW_ORDER_NUMBER);
        String payId = rs.getString(PAY_ID);
        Integer initResultCode = DbUtils.getInteger(rs, INIT_RESULT_CODE);
        String initResultMessage = rs.getString(INIT_RESULT_MESSAGE);
        Integer processResultCode = DbUtils.getInteger(rs, PROCESS_RESULT_CODE);
        String processResultMessage = rs.getString(PROCESS_RESULT_MESSAGE);
        Integer paymentStatus = DbUtils.getInteger(rs, PAYMENT_STATUS);
        String authCode = rs.getString(AUTH_CODE);
        String customerCode = rs.getString(CUSTOMER_CODE);

        return new CsobGwPayment(payment, orderNumber)
                .withInitResult(payId, initResultCode, initResultMessage, paymentStatus);
    }
}
