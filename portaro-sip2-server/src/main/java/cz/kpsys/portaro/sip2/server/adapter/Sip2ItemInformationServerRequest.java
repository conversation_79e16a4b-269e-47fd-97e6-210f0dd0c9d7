package cz.kpsys.portaro.sip2.server.adapter;

import com.ceridwen.circulation.SIP.messages.ItemInformation;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.server.Sip2ServerRequest;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

public record Sip2ItemInformationServerRequest(@NonNull ItemInformation message) implements Sip2ServerRequest<ItemInformation>, TerminalPasswordedServerRequest {

    @NonNull
    @Override
    public CommandType type() {
        return CommandType.ITEM_INFORMATION_REQUEST;
    }

    @Nullable
    @Override
    public String getTerminalPassword() {
        return message.getTerminalPassword();
    }

    @NonNull
    @NotBlank
    public String getItemIdentifier() {
        return StringUtil.requireNotBlank(message.getItemIdentifier(), "Item identifier cannot be empty");
    }
}
