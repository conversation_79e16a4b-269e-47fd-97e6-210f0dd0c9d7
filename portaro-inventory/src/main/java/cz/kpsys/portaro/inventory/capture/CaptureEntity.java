package cz.kpsys.portaro.inventory.capture;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import jakarta.persistence.AttributeOverride;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

import static cz.kpsys.portaro.commons.object.Identified.PROPERTY_ID;
import static cz.kpsys.portaro.databasestructure.InventoryDb.REVI_CAPTURE.*;

@Entity
@Table(name = TABLE)
@AttributeOverride(name = PROPERTY_ID, column = @Column(name = ID_REVI_CAPTURE))
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class CaptureEntity extends BasicIdentified<Integer> {

    @Column(name = FK_REVI)
    Integer inventoryId;

    @Column(name = KOD)
    String identifier;

    @Column(name = CIS_EX)
    Integer exemplarId;

    @Column(name = POCET)
    Integer quantity;

    @Column(name = TYP_ZADANI)
    Integer captureWayId;

    public CaptureEntity(Integer id, Integer inventoryId, String identifier, Integer exemplarId, Integer quantity, Integer captureWayId) {
        super(id);
        this.inventoryId = inventoryId;
        this.identifier = identifier;
        this.exemplarId = exemplarId;
        this.quantity = quantity;
        this.captureWayId = captureWayId;
    }

    public Optional<Integer> getExemplarId() {
        return Optional.ofNullable(exemplarId);
    }

}
