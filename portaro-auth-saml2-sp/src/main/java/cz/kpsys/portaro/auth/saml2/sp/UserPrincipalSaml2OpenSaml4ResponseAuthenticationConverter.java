package cz.kpsys.portaro.auth.saml2.sp;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.user.RoleNamesResolver;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.saml2.provider.service.authentication.OpenSaml4AuthenticationProvider;
import org.springframework.security.saml2.provider.service.authentication.Saml2AuthenticatedPrincipal;
import org.springframework.security.saml2.provider.service.authentication.Saml2Authentication;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserPrincipalSaml2OpenSaml4ResponseAuthenticationConverter implements Converter<OpenSaml4AuthenticationProvider.ResponseToken, Saml2Authentication> {

    @NonNull Converter<OpenSaml4AuthenticationProvider.ResponseToken, Saml2Authentication> originalConverter = OpenSaml4AuthenticationProvider.createDefaultResponseAuthenticationConverter();
    @NonNull Function<Saml2Authentication, User> saml2AuthenticationUserResolver;
    @NonNull RoleNamesResolver roleNamesResolver;

    @Override
    public Saml2Authentication convert(@NonNull OpenSaml4AuthenticationProvider.ResponseToken source) {
        Saml2Authentication original = Objects.requireNonNull(originalConverter.convert(source));
        User user = saml2AuthenticationUserResolver.apply(original);
        Map<String, List<Object>> saml2Attributes = ((Saml2AuthenticatedPrincipal) original.getPrincipal()).getAttributes();
        List<SimpleGrantedAuthority> authorities = ListUtil.convert(roleNamesResolver.resolveRoles(user), SimpleGrantedAuthority::new);

        return new Saml2Authentication(
                new UserSaml2AuthenticatedPrincipal(user, saml2Attributes),
                original.getSaml2Response(),
                authorities
        );
    }

}
