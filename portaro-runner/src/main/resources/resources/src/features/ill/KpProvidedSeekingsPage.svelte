<script lang="ts">
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import SeekingService from 'src/features/ill/seeking.service';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpSearchPageContainerWithContextActions from 'src/features/search/search-page-container-with-context-actions/KpSearchPageContainerWithContextActions.svelte';

    const service = getInjector().getByClass(SeekingService);
    const localize = getLocalization();
    const staticParams = {
        type: SearchType.TYPE_PROVIDED_SEEKING_SEARCH,
        kind: [Kind.KIND_SEEKING]
    };
</script>

<KpSearchPageContainerWithContextActions pageClass="kp-provided-seekings-page" {staticParams}>
    <svelte:fragment slot="context-actions">
        <KpButton on:click={() => service.createProvidedSeekingAndReload()}>
            {localize(/* @kp-localization ill.provision.CreateEffectiveRequest */ 'ill.provision.CreateEffectiveRequest')}
        </KpButton>
    </svelte:fragment>
</KpSearchPageContainerWithContextActions>