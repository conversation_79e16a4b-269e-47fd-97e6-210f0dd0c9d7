package cz.kpsys.portaro.view.web.rest.file;

import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.With;
import org.springframework.lang.Nullable;

@Form(id = "{commons.Uprava}")
@FormSubmit(path = DirectoryApiController.NEW_PATH)
@With
public record DirectoryCreationRequest(

        @Nullable
        Directory parentDirectory,

        @FormPropertyLabel("{commons.nazev}")
        @TextEditor
        @Size(min = 1, max = Directory.NAME_MAX_LENGTH)
        @NotBlank
        String name

) {}
