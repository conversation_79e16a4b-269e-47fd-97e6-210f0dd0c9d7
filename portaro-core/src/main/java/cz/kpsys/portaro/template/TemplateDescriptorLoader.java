package cz.kpsys.portaro.template;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ListUtil;

import java.util.List;

public interface TemplateDescriptorLoader extends AllValuesProvider<TemplateDescriptor> {

    /**
     * Vrati dostupne sablony daneho typu.
     */
    default List<TemplateDescriptor> getAllByType(final String type) {
        return ListUtil.filter(getAll(), template -> template.isOfType(type));
    }

}
