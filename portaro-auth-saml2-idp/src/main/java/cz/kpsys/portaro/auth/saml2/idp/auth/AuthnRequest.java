package cz.kpsys.portaro.auth.saml2.idp.auth;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.time.Instant;

import static org.opensaml.saml.common.xml.SAMLConstants.SAML2_POST_BINDING_URI;

@JacksonXmlRootElement(namespace = "urn:oasis:names:tc:SAML:2.0:protocol", localName = "AuthnRequest")
@Value
public class AuthnRequest {

    public static final String DEFAULT_PROTOCOL_BINDING = SAML2_POST_BINDING_URI;

    /**
     * Mutually exclusive with assertionConsumerServiceIndex
     * e.g. "https://knihovna-opac.tul.cz/login/saml/sso"
     */
    @JacksonXmlProperty(isAttribute = true, localName = "AssertionConsumerServiceURL")
    @Nullable
    String assertionConsumerServiceURL;

    /**
     * Mutually exclusive with assertionConsumerServiceURL
     * e.g. "1".
     */
    @JacksonXmlProperty(isAttribute = true, localName = "AssertionConsumerServiceIndex")
    @Nullable
    Integer assertionConsumerServiceIndex;

    /**
     * e.g. "https://shibbo.tul.cz/idp/profile/SAML2/Redirect/SSO"
     */
    @JacksonXmlProperty(isAttribute = true, localName = "Destination")
    @NonNull
    String destination;

    /**
     * e.g. "false"
     */
    @JsonDeserialize(using = NumericBooleanDeserializer.class)
    @JacksonXmlProperty(isAttribute = true, localName = "ForceAuthn")
    @Nullable
    Boolean forceAuthn;

    /**
     * e.g. "2020-09-21T13:32:01.917Z"
     */
    @JacksonXmlProperty(isAttribute = true, localName = "IssueInstant")
    @NonNull
    Instant issueInstant;

    /**
     * e.g. "ARQ621a4f0-f334-41cf-924c-41bea6a2f2c7"
     */
    @JacksonXmlProperty(isAttribute = true, localName = "ID")
    @NonNull
    String id;

    /**
     * e.g. "false"
     */
    @JsonDeserialize(using = NumericBooleanDeserializer.class)
    @JacksonXmlProperty(isAttribute = true, localName = "IsPassive")
    @Nullable
    Boolean isPassive;

    /**
     * e.g. "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
     */
    @JacksonXmlProperty(isAttribute = true, localName = "ProtocolBinding")
    @Nullable
    String protocolBinding;

    /**
     * e.g. "2.0"
     */
    @JacksonXmlProperty(isAttribute = true, localName = "Version")
    @NonNull
    String version;

    /**
     * e.g. "https://knihovna-opac.tul.cz/login/saml/metadata"
     */
    @JacksonXmlProperty(namespace = "urn:oasis:names:tc:SAML:2.0:assertion", localName = "Issuer")
    @NonNull
    String issuer;


    public static class NumericBooleanDeserializer extends JsonDeserializer<Boolean> {
        @Override
        public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String valueAsString = p.getValueAsString();
            if (valueAsString.equals("true") || valueAsString.equals("1")) {
                return true;
            }
            if (valueAsString.equals("false") || valueAsString.equals("0")) {
                return false;
            }
            throw new JsonParseException(p, "Current token (VALUE_STRING) not of boolean type, but %s".formatted(valueAsString));
        }
    }

}
