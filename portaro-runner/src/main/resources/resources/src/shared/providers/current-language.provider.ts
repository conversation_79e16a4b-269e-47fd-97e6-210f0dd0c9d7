import type {IWindowService} from 'angular';

declare global {
    interface Window {
        currentLanguage: string | undefined;
    }
}

export const SHOW_LOC_MESSAGES_LANGUAGE = 'xx';

export class CurrentLanguageProvider {
    public static providerName = 'currentLanguage';

    public readonly $get: (_: IWindowService) => string;

    /*@ngInject*/
    constructor(private $windowProvider: IWindowService) {
        this.$get = /*@ngInject*/ ($window: IWindowService) => $window.currentLanguage;
    }

    public getCurrentLanguage(): string {
        return this.$windowProvider.$get().currentLanguage;
    }
}