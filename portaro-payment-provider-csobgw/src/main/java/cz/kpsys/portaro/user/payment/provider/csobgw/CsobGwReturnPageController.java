package cz.kpsys.portaro.user.payment.provider.csobgw;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.payment.Payment;
import cz.kpsys.portaro.security.AccessDeniedException;
import cz.kpsys.portaro.user.payment.provider.csobgw.datatypes.CsobGwPaymentResponse;
import cz.kpsys.portaro.web.GenericPageController;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

@RequestMapping
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CsobGwReturnPageController extends GenericPageController {

    @NonNull IsolatedAuthSecuredCsobGwPaymentUpdater csobGwPaymentUpdater;
    @NonNull CsobGwPaymentLoader csobGwPaymentLoader;
    @NonNull CsobGwTemplate csobGwTemplate;
    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull NumberFormat currencyFormat;
    @NonNull ModelAndPageViewFactory modelAndPageViewFactory;

    @RequestMapping("/payment/csobgw/return")
    public ModelAndView paymentReturn(@RequestParam(value = CsobGwConstants.PARAM_PAYID) String payId,
                                      @RequestParam(value = CsobGwConstants.PARAM_TIMESTAMP) String timestamp,
                                      @RequestParam(value = CsobGwConstants.PARAM_RESULTCODE) Integer resultCode,
                                      @RequestParam(value = CsobGwConstants.PARAM_RESULTMESSAGE) String resultMessage,
                                      @RequestParam(value = CsobGwConstants.PARAM_PAYMENTSTATUS) Integer paymentStatus,
                                      @RequestParam(value = CsobGwConstants.PARAM_AUTHCODE, required = false) String authCode,
                                      @RequestParam(value = CsobGwConstants.PARAM_SIGNATURE) String signature,
                                      Locale locale,
                                      @CurrentDepartment Department ctx,
                                      HttpServletRequest request) {
        CsobGwPaymentResponse orderResult = new CsobGwPaymentResponse(payId, timestamp, resultCode, resultMessage, paymentStatus, authCode, null, null, signature);
        log.info("Returned from Csob Gateway pay (payId {})", orderResult.getPayId());

        CsobGwPayment payment = csobGwPaymentLoader.getByCsobGwPayId(orderResult.getPayId());
        payment = csobGwPaymentUpdater.updateResultState(payment, orderResult, ctx);
        Map<String, Object> model = Map.of(
                "message", createResultMessage(payment, locale),
                "origin", String.format("%s/#!/users/%s", publicContextPath.get(), payment.getPayer().getId())
        );
        return modelAndPageViewFactory.pageView("message-page", ctx, request, model, List.of());
    }

    // Toto pridano pouze kvuli testovacimu scenari v CSOB
    @RequestMapping("/payment/csobgw/reverse")
    public void reversePayment(@RequestParam(value = CsobGwConstants.PARAM_PAYID) String payId,
                               @CurrentDepartment Department ctx) {
        CsobGwPaymentResponse paymentStatusTest = csobGwTemplate.getPaymentStatus(payId, ctx);
        if (Objects.equals(paymentStatusTest.getPaymentStatus(), PaymentStatusCode.PAYMENT_WAITING_FOR_POSTING.getId())
            || Objects.equals(paymentStatusTest.getPaymentStatus(), PaymentStatusCode.PAYMENT_CONFIRMED.getId())) {
            CsobGwPaymentResponse orderResult = csobGwTemplate.reversePayment(payId, ctx);
            boolean resultVerified = csobGwTemplate.verifyOrderResult(orderResult, ctx);
            AccessDeniedException.throwIfNot(resultVerified, "Returned result was not verified", Texts.ofNative("Returned result was not verified"));
        }
    }


    private String createResultMessage(Payment payment, Locale locale) {
        return switch (payment.getState()) {
            case CREATED -> String.format("Platba %s byla vytvořena, čeká se na její přijetí", currencyFormat.format(payment.getSumToPay()));
            case PAID -> String.format("Platba %s byla úspěšně provedena", currencyFormat.format(payment.getSumToPay()));
            case CANCELED -> String.format("Platba %s byla zrušena", currencyFormat.format(payment.getSumToPay()));
            default -> String.format("Platba %s je ve stavu %s", currencyFormat.format(payment.getSumToPay()), payment.getState().getText());
        };
    }
}
