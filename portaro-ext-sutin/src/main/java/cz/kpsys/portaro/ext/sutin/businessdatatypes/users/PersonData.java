package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinWorkerResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.*;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.Gender;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PracStatniPrislusnostKat;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PracVzdelaniKat;
import cz.kpsys.portaro.user.PhoneNumberEditationCommand;
import cz.kpsys.portaro.user.contact.UserEmailEditationCommand;
import cz.kpsys.portaro.user.edit.command.UserAddressEditationCommand;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.AdresaZemeKat.CZ_CODE;
import static cz.kpsys.portaro.user.contact.SourceOfData.EXTERNAL_SUTIN;

public record PersonData(

        @NullableNotBlank String firstName,

        @NullableNotBlank String firstName2,

        @NullableNotBlank String lastName,

        @NullableNotBlank String lastName2,

        @NullableNotBlank String prefixDegree,

        @NullableNotBlank String suffixDegree,

        LocalDate birthDate,

        @NullableNotBlank String birthPlace,

        @NullableNotBlank String personalIdentificationNumber,

        @Nullable PracVzdelaniKat educationLevel,

        @NullableNotBlank String educationInstitution,

        @Nullable PracStatniPrislusnostKat nationality,

        @NullableNotBlank String extension,

        @Nullable Gender gender,

        String pohodaId,

        @NullableNotBlank String username,

        List<UserAddressEditationCommand> addresses,

        List<UserEmailEditationCommand> emails,

        List<PhoneNumberEditationCommand> phoneNumbers

) {

    public static PersonData fromSutinWorkerData(@NonNull SutinWorkerResponse worker) {
        return new PersonData(
                worker.employeeData().firstName(),
                worker.employeeData().firstName2(),
                worker.employeeData().lastName(),
                worker.employeeData().lastName2(),
                worker.employeeData().degreeBefore(),
                worker.employeeData().degreeAfter(),
                worker.employeeData().birthDate(),
                worker.employeeData().birthPlace(),
                worker.employeeData().personalIdentificationNumber(),
                worker.employeeData().educationLevel(),
                worker.employeeData().educationInstitution(),
                worker.employeeData().nationality(),
                worker.employeeData().extension(),
                worker.employeeData().gender(),
                worker.employeeData().pohodaId(),
                null,
                getUserAddressRequests(worker.addresses()),
                getEmailRequests(worker.contacts()),
                getPhoneNumberRequests(worker)
        );
    }

    @NonNull
    private static List<UserEmailEditationCommand> getEmailRequests(List<Kontakt> sutinContact) {
        return sutinContact.stream()
                .filter(kontakt -> kontakt.type().equals(KontaktTypKat.EMAIL))
                .map(kontakt -> UserEmailEditationCommand.createValidated(fixForbiddenCharacters(kontakt.value()), EXTERNAL_SUTIN))
                .toList();
    }

    private static String fixForbiddenCharacters(String input) {
        return input.replace('ö', 'o').replace('ü', 'u').replace(' ', 'X');
    }

    @NonNull
    private static List<PhoneNumberEditationCommand> getPhoneNumberRequests(@NonNull SutinWorkerResponse worker) {
        return worker.contacts().stream()
                .filter(kontakt -> kontakt.type().equals(KontaktTypKat.TELEFON))
                .flatMap(kontakt -> Arrays.stream(kontakt.value().split(",")))
                .map(String::trim)
                .map(kontakt -> PhoneNumberEditationCommand.createValidated(kontakt, true, EXTERNAL_SUTIN))
                .toList();
    }


    private static List<UserAddressEditationCommand> getUserAddressRequests(List<AdresaWithType> sutinAddresses) {
        return sutinAddresses.stream()
                .map(addressWithType -> {

                    boolean isResident = addressWithType.addressType().stream()
                            .anyMatch(adresaTyp -> adresaTyp.adresaTyp().equals(AdresaTypKat.TRVALE_BYDLISTE));

                    boolean isMailing = addressWithType.addressType().stream()
                            .anyMatch(adresaTyp -> adresaTyp.adresaTyp().equals(AdresaTypKat.DORUCOVACI));

                    // TODO bohuzel chyby v SUTIN datech
                    int countryId;
                    if (addressWithType.address().countryId() == null) {
                        countryId = CZ_CODE.getId();
                    } else {
                        countryId = addressWithType.address().countryId();
                    }

                    return new UserAddressEditationCommand(
                            isResident,
                            isMailing,
                            EXTERNAL_SUTIN,
                            addressWithType.address().street(),
                            addressWithType.address().city(),
                            addressWithType.address().postalCode(),
                            Country.getByAlpha2(AdresaZemeKat.CODEBOOK.getById(countryId).getValue())
                    );
                })
                .toList();
    }

}
