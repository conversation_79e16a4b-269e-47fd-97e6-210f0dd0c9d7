package cz.kpsys.portaro.proveniencesmap;

@SuppressWarnings("TypeName")
public class ProveniencesDb {
    public static class OPAC_PLACES_HISTORY {
        public static final String TABLE = "opac_places_history";
        public static final String ID = "id";
        public static final String RECORD_ID = "record_id";
        public static final String TITLE = "title";
        public static final String TYPE_RECORD_ID = "type_record_id";
        public static final String YEAR_FROM = "year_from";
        public static final String YEAR_TO = "year_to";
        public static final String LAT = "lat";
        public static final String LNG = "lng";
        public static final String PLACE = "place";
        public static final String PROFESSION = "profession";
        public static final String SEX = "sex";
    }

    public static class PLACES_HISTORY_TYPE_RECORD {
        public static final String TABLE = "places_history_type_record";
        public static final String ID = "id";
        public static final String PLACES_ID = "places_id";
        public static final String TYPE_RECORD_ID = "type_record_id";
    }
}
