package cz.kpsys.portaro.auth.bankid.notify;

import com.nimbusds.jose.KeySourceException;
import com.nimbusds.jose.proc.JWSAlgorithmFamilyJWSKeySelector;
import com.nimbusds.jose.proc.JWSKeySelector;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.JWTClaimNames;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.proc.DefaultJWTClaimsVerifier;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import cz.kpsys.portaro.auth.bankid.BankIDUserLoader;
import cz.kpsys.portaro.auth.bankid.BankIDUserReloader;
import cz.kpsys.portaro.auth.oauth2.OAuth2Provider;
import cz.kpsys.portaro.auth.oauth2.jwt.ClaimObjectToValueConverter;
import cz.kpsys.portaro.auth.oauth2.jwt.ValidatedSpringJwtClaimsDecoder;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.util.UrlUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.token.Auth0TokenExpirationResolver;
import cz.kpsys.portaro.token.ClaimsDecoder;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;

import java.net.URL;
import java.util.Set;

@Configuration
@Lazy
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BankIDNotifyConfig {

    @NonNull BankIDUserLoader bankIDUserLoader;
    @NonNull BankIDUserReloader bankIDUserReloader;
    @NonNull ContextualFunction<OAuth2Provider, Department, ClientRegistration> clientRegistrationByIdContextualLoader;
    @NonNull ClaimObjectToValueConverter claimObjectToValueConverter;

    @Bean
    public ContextualProvider<Department, ClaimsDecoder> bankIDClaimDecoderProvider() {
        return (ctx) -> {
            JWSKeySelector<SecurityContext> jwsKeySelector = bankIDJWSKeySelector(ctx);

            DefaultJWTProcessor<SecurityContext> jwtProcessorTwo = new DefaultJWTProcessor<>();
            jwtProcessorTwo.setJWSKeySelector(jwsKeySelector);
            jwtProcessorTwo.setJWTClaimsSetVerifier(bankIDJWTClaimsVerifier(ctx));
            JwtDecoder springNimbusJwtDecoder = new NimbusJwtDecoder(jwtProcessorTwo);

            return new ValidatedSpringJwtClaimsDecoder(
                    springNimbusJwtDecoder,
                    new Auth0TokenExpirationResolver(),
                    claimObjectToValueConverter
            );
        };

    }

    private @NonNull DefaultJWTClaimsVerifier<SecurityContext> bankIDJWTClaimsVerifier(Department ctx) {
        return new DefaultJWTClaimsVerifier<>(
                new JWTClaimsSet.Builder().issuer(bankIDJwtIssuerProvider().getOn(ctx)).build(),
                Set.of(
                        JWTClaimNames.ISSUER,
                        JWTClaimNames.ISSUED_AT,
                        JWTClaimNames.JWT_ID,
                        BankIDNotificationClaimFields.EVENTS.name()
                ));
    }

    private @NonNull JWSKeySelector<SecurityContext> bankIDJWSKeySelector(Department ctx) {
        try {
            return JWSAlgorithmFamilyJWSKeySelector.fromJWKSetURL(bankIDJwksUrlProvider().getOn(ctx));
        } catch (KeySourceException e) {
            throw new RuntimeException(e);
        }
    }

    @NonNull
    private ContextualProvider<Department, @NonNull URL> bankIDJwksUrlProvider() {
        return clientRegistrationByIdContextualLoader.andThen(ClientRegistration::getProviderDetails).andThen(ClientRegistration.ProviderDetails::getJwkSetUri).andThen(UrlUtils::createUrl)
                .toProvider(OAuth2Provider.BANKID);
    }

    @NonNull
    private ContextualProvider<Department, @NonNull String> bankIDJwtIssuerProvider() {
        return clientRegistrationByIdContextualLoader.andThen(ClientRegistration::getProviderDetails).andThen(ClientRegistration.ProviderDetails::getIssuerUri).toProvider(OAuth2Provider.BANKID);
    }

    @Bean
    public BankIDNotifyController bankIDNotifyController() {
        return new BankIDNotifyController(bankIDNotificationDecoderAndVerifier(), bankIDNotificationHandler());
    }

    @Bean
    public BankIDNotificationHandler bankIDNotificationHandler() {
        return new BankIDNotificationHandler(bankIDUserLoader, bankIDUserReloader);
    }

    @Bean
    public BankIDNotificationDecoderAndVerifier bankIDNotificationDecoderAndVerifier() {
        return new BankIDNotificationDecoderAndVerifier(bankIDClaimDecoderProvider());
    }

}
