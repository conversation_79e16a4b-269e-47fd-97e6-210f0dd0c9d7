package cz.kpsys.portaro.sip2.server.ceridwen;

import com.ceridwen.circulation.SIP.messages.*;
import com.ceridwen.circulation.SIP.netty.server.driver.Driver;
import com.ceridwen.circulation.SIP.netty.server.driver.operation.*;
import cz.kpsys.portaro.sip2.server.Sip2Handler;
import cz.kpsys.portaro.sip2.server.Sip2ServerRequest;
import cz.kpsys.portaro.sip2.server.Sip2ServerResponse;
import cz.kpsys.portaro.sip2.server.adapter.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.format.DateTimeFormatter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class Sip2HandlerAdapterDispatchingDriver implements
        Driver,
        BlockPatronOperation,
        CheckInOperation,
        CheckOutOperation,
        EndPatronSessionOperation,
        FeePaidOperation,
        HoldOperation,
        ItemInformationOperation,
        ItemStatusUpdateOperation,
        LoginOperation,
        PatronEnableOperation,
        PatronInformationOperation,
        PatronStatusOperation,
        RenewAllOperation,
        RenewOperation {


    @NonNull Sip2Handler<Sip2ServerRequest<?>, Sip2ServerResponse<?>> sip2Handler;
    @NonNull DateTimeFormatter dueDateFormatter;

    @Override
    public ACSStatus Status(SCStatus req) {
        Sip2StatusServerRequest request = new Sip2StatusServerRequest(req);
        Sip2StatusServerResponse response = new Sip2StatusServerResponse(new ACSStatus());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public PatronStatusResponse BlockPatron(BlockPatron req) {
        Sip2BlockPatronServerRequest request = new Sip2BlockPatronServerRequest(req);
        Sip2PatronStatusServerResponse response = new Sip2PatronStatusServerResponse(new PatronStatusResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public CheckInResponse CheckIn(CheckIn req) {
        Sip2CheckinServerRequest request = new Sip2CheckinServerRequest(req);
        Sip2CheckinServerResponse response = new Sip2CheckinServerResponse(new CheckInResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public CheckOutResponse CheckOut(CheckOut req) {
        Sip2CheckoutServerRequest request = new Sip2CheckoutServerRequest(req);
        Sip2CheckoutServerResponse response = new Sip2CheckoutServerResponse(new CheckOutResponse(), dueDateFormatter);
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public EndSessionResponse EndPatronSession(EndPatronSession req) {
        Sip2EndPatronSessionServerRequest request = new Sip2EndPatronSessionServerRequest(req);
        Sip2EndPatronSessionServerResponse response = new Sip2EndPatronSessionServerResponse(new EndSessionResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public FeePaidResponse FeePaid(FeePaid req) {
        Sip2FeePaidServerRequest request = new Sip2FeePaidServerRequest(req);
        Sip2FeePaidServerResponse response = new Sip2FeePaidServerResponse(new FeePaidResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public HoldResponse Hold(Hold req) {
        Sip2HoldServerRequest request = new Sip2HoldServerRequest(req);
        Sip2HoldServerResponse response = new Sip2HoldServerResponse(new HoldResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public ItemInformationResponse ItemInformation(ItemInformation req) {
        Sip2ItemInformationServerRequest request = new Sip2ItemInformationServerRequest(req);
        Sip2ItemInformationServerResponse response = new Sip2ItemInformationServerResponse(new ItemInformationResponse(), dueDateFormatter);
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public ItemStatusUpdateResponse ItemStatusUpdate(ItemStatusUpdate req) {
        Sip2ItemStatusUpdateServerRequest request = new Sip2ItemStatusUpdateServerRequest(req);
        Sip2ItemStatusUpdateServerResponse response = new Sip2ItemStatusUpdateServerResponse(new ItemStatusUpdateResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public LoginResponse Login(Login req) {
        Sip2LoginServerRequest request = new Sip2LoginServerRequest(req);
        Sip2LoginServerResponse response = new Sip2LoginServerResponse(new LoginResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public PatronEnableResponse PatronEnable(PatronEnable req) {
        Sip2PatronEnableServerRequest request = new Sip2PatronEnableServerRequest(req);
        Sip2PatronEnableServerResponse response = new Sip2PatronEnableServerResponse(new PatronEnableResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public PatronInformationResponse PatronInformation(PatronInformation req) {
        Sip2PatronInformationServerRequest request = new Sip2PatronInformationServerRequest(req);
        Sip2PatronInformationServerResponse response = new Sip2PatronInformationServerResponse(new PatronInformationResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public PatronStatusResponse PatronStatus(PatronStatusRequest req) {
        Sip2PatronStatusServerRequest request = new Sip2PatronStatusServerRequest(req);
        Sip2PatronStatusServerResponse response = new Sip2PatronStatusServerResponse(new PatronStatusResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public RenewResponse Renew(Renew req) {
        Sip2RenewServerRequest request = new Sip2RenewServerRequest(req);
        Sip2RenewServerResponse response = new Sip2RenewServerResponse(new RenewResponse(), dueDateFormatter);
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

    @Override
    public RenewAllResponse RenewAll(RenewAll req) {
        Sip2RenewAllServerRequest request = new Sip2RenewAllServerRequest(req);
        Sip2RenewAllServerResponse response = new Sip2RenewAllServerResponse(new RenewAllResponse());
        sip2Handler.handle(request, response);
        return response.getMessage();
    }

}
