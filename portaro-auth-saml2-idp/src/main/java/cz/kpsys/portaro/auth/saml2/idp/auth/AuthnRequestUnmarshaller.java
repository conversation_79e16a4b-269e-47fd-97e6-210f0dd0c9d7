package cz.kpsys.portaro.auth.saml2.idp.auth;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.convert.ConversionException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.zip.Inflater;
import java.util.zip.InflaterOutputStream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AuthnRequestUnmarshaller {

    @NonNull ObjectMapper xmlMapper;

    public AuthnRequest unmarshallHttpParameterRequest(@NonNull String base64SamlRequest) {
        byte[] base64Decoded = base64Decode(base64SamlRequest);
        String xml = inflate(base64Decoded);
        return unmarshallXml(xml);
    }

    public AuthnRequest unmarshallXml(String xml) {
        try {
            return xmlMapper.readValue(xml, AuthnRequest.class);
        } catch (Throwable e) {
            throw new ConversionException("Cannot read SAMLRequest xml: %s, xml is:\n%s".formatted(e, xml), e);
        }
    }

    private String inflate(byte[] base64Decoded) {
        try {
            return samlInflate(base64Decoded);
        } catch (Throwable e) {
            throw new ConversionException("Cannot inflate base64-decoded SAMLRequest parameter: %s".formatted(e), e);
        }
    }

    private byte[] base64Decode(String base64SamlRequest) {
        try {
            return samlDecode(base64SamlRequest);
        } catch (Throwable e) {
            throw new ConversionException("Cannot base64-decode SAMLRequest parameter: %s".formatted(e), e);
        }
    }

    static byte[] samlDecode(String s) {
        return new Base64(0, new byte[] {'\n'}).decode(s);
    }

    @SneakyThrows
    static String samlInflate(byte[] b) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        InflaterOutputStream iout = new InflaterOutputStream(out, new Inflater(true));
        iout.write(b);
        iout.finish();
        return out.toString(StandardCharsets.UTF_8.name());
    }
}
