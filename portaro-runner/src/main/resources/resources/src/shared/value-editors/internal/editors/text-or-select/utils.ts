import {
    maxLengthValidator,
    minLengthValidator,
    notBlankValidator,
    patternValidator,
    requiredValidator
} from '../../shared/validators';
import type {Validator} from 'svelte-forms';
import {exists, isDefined} from '../../../../utils/custom-utils';
import type {TextValueEditorValidations} from '../text/types';
import type {TextOrSelectValueEditorOptions} from './types';

export function validatorFactory(validations: TextValueEditorValidations, formModel: Record<string, unknown>, options: TextOrSelectValueEditorOptions): Validator[] {
    const validators: Validator[] = [];

    if (hasRequiredValidation(validations.required, formModel, options.disableRequireIfFormContains)) {
        validators.push(requiredValidator());
    }

    if (isDefined(validations.maxlength)) {
        validators.push(maxLengthValidator(validations.maxlength));
    }

    if (isDefined(validations.minlength)) {
        validators.push(minLengthValidator(validations.minlength));
    }

    if (isDefined(validations.notBlank) && validations.notBlank) {
        validators.push(notBlankValidator());
    }

    if (isDefined(validations.pattern)) {
        validators.push(patternValidator(new RegExp(validations.pattern)));
    }

    return validators;
}

export function hasFormModelPropertyOf(formModel: Record<string, unknown>, formModelProperty: string): boolean {
    return exists(formModel) && formModelProperty in formModel;
}

export function hasRequiredValidation(required: boolean, formModel: Record<string, unknown>, disableRequireIfFormContains: string) {
    return isDefined(required) && required === true && !hasFormModelPropertyOf(formModel, disableRequireIfFormContains)
}