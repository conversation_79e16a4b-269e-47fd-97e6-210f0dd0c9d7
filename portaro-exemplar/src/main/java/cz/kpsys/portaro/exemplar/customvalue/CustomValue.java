package cz.kpsys.portaro.exemplar.customvalue;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;

public class CustomValue extends BasicNamedLabeledIdentified<String> {

    public CustomValue(String id, String name) {
        super(id, name);
    }

    public static CustomValue testing(String id) {
        return new CustomValue(id, "CustomValue_" + id);
    }

    @Override
    public Text getText() {
        return Texts.ofColumnMessageCodedOrNativeOrEmptyNative(getName(), "DEF_STAVZ", "POPIS", getId());
    }
    
}