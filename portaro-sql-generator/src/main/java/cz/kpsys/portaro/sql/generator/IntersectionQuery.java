package cz.kpsys.portaro.sql.generator;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;

public class IntersectionQuery extends Query {
    
    private final List<SelectQuery> selectQueries;
    private String intersectionColumn;
    private @Nullable Integer page;
    private @Nullable Integer pageSize;
    private boolean count;

    
    public IntersectionQuery(DbSpecifics dbSpecifics) {
        super(dbSpecifics);
        count = false;
        selectQueries = new ArrayList<>();
    }
    
    

    @Override
    public String toString() {
        String query = getDbSpecifics().getIntersectionQuery(intersectionColumn, selectQueries);
        if (page != null && pageSize != null) {
            query = getDbSpecifics().getPaginatedQuery(query, page, pageSize);
        } else if (count) {
            query = "select count(*) from (" + query + ")";
        }
        return query;
    }
    
    
    

    public void setPageAndSize(@NonNull Integer page, @NonNull Integer pageSize) {
        this.page = page;
        this.pageSize = pageSize;
    }

    public void setIntersectionColumn(String intersectionColumn) {
        this.intersectionColumn = intersectionColumn;
    }
    
    public void setIntersectionColumnCount(String intersectionColumn) {
        setIntersectionColumn(intersectionColumn);
        count = true;
    }
    
    public void addSelectQuery(SelectQuery selectQuery) {
        selectQueries.add(selectQuery);
        getParamMap().addValues(selectQuery.getParamMap().getValues());
    }
    
}
