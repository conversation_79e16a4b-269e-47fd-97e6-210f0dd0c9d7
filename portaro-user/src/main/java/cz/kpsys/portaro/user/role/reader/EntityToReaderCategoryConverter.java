package cz.kpsys.portaro.user.role.reader;

import cz.kpsys.portaro.commons.util.NumberUtil;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class EntityToReaderCategoryConverter implements Converter<ReaderCategoryEntity, ReaderCategory> {

    @Override
    public ReaderCategory convert(@NonNull ReaderCategoryEntity entity) {
        return new ReaderCategory(
                entity.getId(),
                entity.getName(),
                entity.getRegistrationFee(),
                entity.getRegistrationDurationMonths(),
                entity.getLendingAllowed(),
                entity.getRemindingActive(),
                NumberUtil.nullIfZero(entity.getLoanCountLimit()),
                entity.getRegistrationToDateInFormatDDMM(),
                entity.getInternetSessionLimitMinutes(),
                entity.getGroupable(),
                NumberUtil.nullIfZero(entity.getReservationsCountLimit()),
                NumberUtil.nullIfZero(entity.getOrdersCountLimit())
        );
    }

}


