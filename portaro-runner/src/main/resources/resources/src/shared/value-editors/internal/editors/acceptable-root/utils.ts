import type {<PERSON><PERSON><PERSON>, <PERSON>rse<PERSON>} from '../../forms/types';
import type {AcceptableRootValueEditorOptions, Childrenable} from './types';
import type {ValueEditorValidations} from '../../../kp-value-editor/types';
import type {Validator} from 'svelte-forms';
import {isDefined, isNull, isNullOrUndefined} from '../../../../utils/custom-utils';
import {requiredValidator} from '../../shared/validators';

export function validatorFactory(validations: ValueEditorValidations): Validator[] {
    const validators: Validator[] = [];

    if (isDefined(validations.required) && validations.required) {
        validators.push(requiredValidator());
    }

    return validators;
}

export function modelToIdentifierFormatter<MODEL, ID>(options: AcceptableRootValueEditorOptions<MODEL, ID>): Formatter<MODEL[], ID[]> {
    return (values: MODEL[]) => {
        if (values === null) {
            return null;
        }

        return values.map((value) => getOptionId<MODEL, ID>(value, getOptionIdResolver<MODEL, ID>(options)));
    }
}

// acceptable value can change over time
export function identifierToModelParser<MODEL, ID>(acceptableValueProvider: () => MODEL, options: AcceptableRootValueEditorOptions<MODEL, ID>): Parser<ID[], MODEL[]> {
    return (values: ID[]) => {
        if (values === null) {
            return null;
        }
        return values.map((value) => getOptionById<MODEL, ID>(acceptableValueProvider(), getOptionIdResolver<MODEL, ID>(options), value));
    }
}

export function getOptionById<VALUE extends Childrenable<VALUE>, ID>(rootOption: VALUE, idResolver: (_: VALUE) => ID, id: ID): VALUE {
    if (idResolver(rootOption) === id) {
        return rootOption;
    }

    return (rootOption.children ?? [])
        .map((option) => getOptionById(option, idResolver, id))
        .find((option) => !isNullOrUndefined(option));
}

export function getOptionId<VALUE, ID>(option: VALUE,  idResolver: (_: VALUE) => ID): ID {
    if (isNull(option)) {
        return null;
    }
    return idResolver(option);
}

export function getOptionIdResolver<VALUE, ID>(options: AcceptableRootValueEditorOptions<VALUE, ID>): (_: VALUE) => ID {
    return (option) => {
        try {
            return options.optionIdResolver({option});
        } catch (e) {
            throw new Error('Error in custom optionIdResolver', {cause: e});
        }
    }
}