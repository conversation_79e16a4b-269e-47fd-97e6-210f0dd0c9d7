package cz.kpsys.portaro.tcp;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.PrintWriter;
import java.util.concurrent.Callable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ExchangeMessageTask implements Callable<String> {

    @NonNull PrintWriter requestWriter;
    @NonNull BufferedReader responseReader;
    @NonNull String message;

    @SneakyThrows
    @Override
    public String call() {
        requestWriter.println(message);
        return responseReader.readLine();
    }
}
