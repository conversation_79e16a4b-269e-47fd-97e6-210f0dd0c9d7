package cz.kpsys.portaro.ext.obalkyknih.metadata;

import cz.kpsys.portaro.record.Record;
import lombok.experimental.StandardException;

import java.net.URI;

@StandardException
public class NoDataException extends MetadataException {

    public NoDataException(Record record, URI url, Object response) {
        this(String.format("No data received for %s. URL: %s, response: %s", record, url, response));
    }

}
