package cz.kpsys.portaro.business.cancel;

import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SavingCanceller<E> implements Canceller<E> {

    @NonNull Saver<E, ?> saver;

    @Override
    public void cancel(@NonNull E command) {
        saver.save(command);
    }
}
