package cz.kpsys.portaro.loan.ill.persist;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.loan.ill.SeekingConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.Assert;

import java.util.List;

import static cz.kpsys.portaro.commons.db.QueryUtils.COALESCE;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.LoanDb.MVS.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbIllEntitySearchLoader extends AbstractSpringDbSearchLoader<MapBackedParams, IllEntity, RangePaging> {

    @NonNull Provider<Currency> seekingCurrencyProvider;

    public SpringDbIllEntitySearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate, @NonNull QueryFactory queryFactory, @NonNull Provider<Currency> seekingCurrencyProvider) {
        super(jdbcTemplate, queryFactory);
        this.seekingCurrencyProvider = seekingCurrencyProvider;
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        // oboji z INCLUDE_SEEKERS_VIEW a INCLUDE_PROVIDERS_VIEW musi VZDY byt nastaveno
        Assert.state(p.has(SeekingConstants.SearchParams.INCLUDE_SEEKERS_VIEW), "There MUST be %s search param in seeking loader".formatted(SeekingConstants.SearchParams.INCLUDE_SEEKERS_VIEW));
        Assert.state(p.has(SeekingConstants.SearchParams.INCLUDE_PROVIDERS_VIEW), "There MUST be %s search param in seeking loader".formatted(SeekingConstants.SearchParams.INCLUDE_PROVIDERS_VIEW));

        sq.from(TABLE);

        if (!p.get(SeekingConstants.SearchParams.INCLUDE_SEEKERS_VIEW)) {
            sq.where().and().eq(TC(TABLE, JE_AKTIV), true);
        }

        if (!p.get(SeekingConstants.SearchParams.INCLUDE_PROVIDERS_VIEW)) {
            sq.where().and().eq(TC(TABLE, JE_AKTIV), false);
        }

        if (p.has(SeekingConstants.SearchParams.SEEKING_ID)) {
            List<Integer> realizationIds = p.get(SeekingConstants.SearchParams.SEEKING_ID);
            if (realizationIds.isEmpty()) {
                return false;
            }
            sq.where().and().in(TC(TABLE, ID_MVS), realizationIds);
        }

        if (p.has(SeekingConstants.SearchParams.SEEKING_REQUESTER_USER)) {
            if (!p.hasLength(SeekingConstants.SearchParams.SEEKING_REQUESTER_USER)) {
                return false;
            }
            sq.where().and().in(TC(TABLE, FK_UZIV_CTEN), getListOfIds(p.get(SeekingConstants.SearchParams.SEEKING_REQUESTER_USER)));
        }

        if (p.has(SeekingConstants.SearchParams.SEEKING_PROVIDER_USER)) {
            if (!p.hasLength(SeekingConstants.SearchParams.SEEKING_PROVIDER_USER)) {
                return false;
            }
            sq.where()
                    .and().eq(TC(TABLE, JE_AKTIV), false)
                    .and().in(TC(TABLE, FK_UZIV_KNIH), getListOfIds(p.get(SeekingConstants.SearchParams.SEEKING_PROVIDER_USER)));
        }

        if (p.has(SeekingConstants.SearchParams.SEEKING_SEEKER_USER)) {
            if (!p.hasLength(SeekingConstants.SearchParams.SEEKING_SEEKER_USER)) {
                return false;
            }
            sq.where()
                    .and().eq(TC(TABLE, JE_AKTIV), true)
                    .and().in(TC(TABLE, FK_UZIV_KNIH), getListOfIds(p.get(SeekingConstants.SearchParams.SEEKING_SEEKER_USER)));
        }

        if (p.has(SeekingConstants.SearchParams.SEEKER_REFERENCE_ID)) {
            if (p.get(SeekingConstants.SearchParams.SEEKER_REFERENCE_ID).isBlank()) {
                return false;
            }
            Brackets brackets = sq.where().and().brackets();
            brackets
                    .eq(TC(TABLE, JE_AKTIV), false)
                    .and()
                    .eq(TC(TABLE, MY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.SEEKER_REFERENCE_ID))
                    .or()
                    .eq(TC(TABLE, JE_AKTIV), true)
                    .and()
                    .eq(TC(TABLE, COUNTERPARTY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.SEEKER_REFERENCE_ID));
        }

        if (p.has(SeekingConstants.SearchParams.PROVIDER_REFERENCE_ID)) {
            if (p.get(SeekingConstants.SearchParams.PROVIDER_REFERENCE_ID).isBlank()) {
                return false;
            }
            Brackets brackets = sq.where().and().brackets();
            brackets
                    .eq(TC(TABLE, JE_AKTIV), false)
                    .and()
                    .eq(TC(TABLE, COUNTERPARTY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.PROVIDER_REFERENCE_ID))
                    .or()
                    .eq(TC(TABLE, JE_AKTIV), true)
                    .and()
                    .eq(TC(TABLE, MY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.PROVIDER_REFERENCE_ID));
        }

        if (p.has(SeekingConstants.SearchParams.SEEKER_REFERENCE_ID_PREFIX)) {
            Brackets brackets = sq.where().and().brackets();
            brackets
                    .eq(TC(TABLE, JE_AKTIV), false)
                    .and()
                    .startsWith(TC(TABLE, MY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.SEEKER_REFERENCE_ID_PREFIX))
                    .or()
                    .eq(TC(TABLE, JE_AKTIV), true)
                    .and()
                    .startsWith(TC(TABLE, COUNTERPARTY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.SEEKER_REFERENCE_ID_PREFIX));
        }

        if (p.has(SeekingConstants.SearchParams.PROVIDER_REFERENCE_ID_PREFIX)) {
            Brackets brackets = sq.where().and().brackets();
            brackets
                    .eq(TC(TABLE, JE_AKTIV), false)
                    .and()
                    .startsWith(TC(TABLE, COUNTERPARTY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.PROVIDER_REFERENCE_ID_PREFIX))
                    .or()
                    .eq(TC(TABLE, JE_AKTIV), true)
                    .and()
                    .startsWith(TC(TABLE, MY_REFERENCE_ID), p.get(SeekingConstants.SearchParams.PROVIDER_REFERENCE_ID_PREFIX));
        }

        if (p.has(CoreSearchParams.DEPARTMENT)) {
            if (!p.hasLength(CoreSearchParams.DEPARTMENT)) {
                return false;
            }
            // fk_pujc je aktualne pro seeked a provided varianty spolecny
            sq.where().and().in(FK_PUJC, getListOfIds(p.get(CoreSearchParams.DEPARTMENT)));
        }

        if (p.has(SeekingConstants.SearchParams.ZISKEJ_TICKET_ID)) {
            if (p.get(SeekingConstants.SearchParams.ZISKEJ_TICKET_ID).isBlank()) {
                return false;
            }
            sq.where().and().eq(TC(TABLE, SYNC_ID), p.get(SeekingConstants.SearchParams.ZISKEJ_TICKET_ID));
        }

        if (p.has(SeekingConstants.SearchParams.ZISKEJ_SUBTICKET_ID)) {
            if (p.get(SeekingConstants.SearchParams.ZISKEJ_SUBTICKET_ID).isBlank()) {
                return false;
            }
            sq.where().and().eq(TC(TABLE, PROV_SYNC_ID), p.get(SeekingConstants.SearchParams.ZISKEJ_SUBTICKET_ID));
        }

        if (p.isPresentAndFalse(SeekingConstants.SearchParams.INCLUDE_NOT_ACCEPTED)) {
            sq.where().and().isNotNull(TC(TABLE, ID_MVS));
        }

        if (p.isPresentAndFalse(SeekingConstants.SearchParams.INCLUDE_RETURNED)) {
            sq.where()
                    .and().isNull(TC(TABLE, DAT_ODE))
                    .and().isNull(TC(TABLE, PROV_PROVIDER_RECEIVE_DATE));
        }

        if (p.isPresentAndFalse(SeekingConstants.SearchParams.INCLUDE_CANCELLED)) {
            sq.where().and().brackets()
                    .isNull(TC(TABLE, FK_STAVMVS))
                    .or()
                    .notEq(TC(TABLE, FK_STAVMVS), MvsState.CANCELLED.getId());
        }

        if (p.isPresentAndFalse(SeekingConstants.SearchParams.INCLUDE_STANDALONE)) {
            sq.where().and().isNotNull(TC(TABLE, SYNC_ID));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.ofDesc(COALESCE(TC(TABLE, REQUEST_CREATE_DATE), TC(TABLE, DAT_POZ)));
    }

    @Override
    protected ResultSetExtractor<Chunk<IllEntity, RangePaging>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @NonNull RangePaging paging, @NonNull Sorting sorting) {
        return new RangePagingResultSetExtractor<>(new IllEntityRowMapper(seekingCurrencyProvider), paging);
    }

}
