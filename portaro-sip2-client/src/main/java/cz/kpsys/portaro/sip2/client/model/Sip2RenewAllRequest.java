package cz.kpsys.portaro.sip2.client.model;

import cz.kpsys.portaro.commons.object.Throolean;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.client.Sip2MessageRequest;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
public class Sip2RenewAllRequest extends Sip2MessageRequest<Sip2RenewAllResponse> {

    /**
     * Date and time of the request.
     */
    @NonNull
    private Instant transactionDate;

    /**
     * Library's institution id.
     */
    @NonNull
    private String institutionId = "";

    /**
     * An identifying value for the patron, library card's barcode number for example.
     */
    @NonNull
    private String patronIdentifier;

    /**
     * Password (PIN) of the patron. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String patronPassword;

    /**
     * Password for the system to login to the ILS. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String terminalPassword;

    /**
     * If false and there's a fee associated with the transaction, the ILS SIP server should tell the system in the response that there's a fee, and refuse to complete the transaction. If the system and the patron then interact and the patron agrees to pay the fee, this field will be set to true on a second request message, indicating to the ILS SIP server that the patron has acknowledged the fee and the transaction should not be refused just because there is a fee associated with it.
     */
    private Throolean feeAcknowledged = Throolean.UNKNOWN;

    public Sip2RenewAllRequest(@NonNull Instant transactionDate, @NonNull String patronId) {
        super(CommandType.RENEW_ALL_REQUEST);
        this.transactionDate = transactionDate;
        this.patronIdentifier = patronId;
    }

    public Sip2RenewAllRequest(@NonNull Instant transactionDate, @NonNull String institutionId, String patronId) {
        this(transactionDate, patronId);
        this.institutionId = institutionId;
    }

    public Sip2RenewAllRequest(@NonNull Instant transactionDate, @NonNull String institutionId, String patronId, String patronPassword) {
        this(transactionDate, patronId);
        this.institutionId = institutionId;
        this.patronPassword = patronPassword;
    }

    public Sip2RenewAllRequest(@NonNull Instant transactionDate, @NonNull String institutionId, String terminalPassword, String patronId, String patronPassword) {
        this(transactionDate, patronId);
        this.institutionId = institutionId;
        this.terminalPassword = terminalPassword;
        this.patronPassword = patronPassword;
    }
}
