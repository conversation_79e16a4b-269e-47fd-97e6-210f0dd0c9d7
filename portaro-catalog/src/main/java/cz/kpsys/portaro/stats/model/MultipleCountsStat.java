package cz.kpsys.portaro.stats.model;

import cz.kpsys.portaro.commons.localization.Text;
import lombok.NonNull;
import lombok.Value;
import org.springframework.data.util.Pair;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BinaryOperator;

@Value
public class MultipleCountsStat<V> {
    
    @NonNull List<LabeledCount<V>> counts;
    @NonNull BinaryOperator<V> summer;
    @NonNull V sumIfEmpty;

    public static MultipleCountsStat<Long> ofEmptyLongs() {
        return ofLongs(new ArrayList<>());
    }

    public static MultipleCountsStat<Long> ofLongs(@NonNull List<LabeledCount<Long>> counts) {
        return new MultipleCountsStat<>(counts, Long::sum, 0L);
    }

    public static MultipleCountsStat<Pair<Long, Long>> ofEmptyLongPairs() {
        return ofLongPairs(new ArrayList<>());
    }

    public static MultipleCountsStat<Pair<Long, Long>> ofLongPairs(@NonNull List<LabeledCount<Pair<Long, Long>>> counts) {
        return new MultipleCountsStat<>(counts, (pair1, pair2) -> Pair.of(pair1.getFirst() + pair2.getFirst(), pair1.getSecond() + pair2.getSecond()), Pair.of(0L, 0L));
    }

    public LabeledCount<V> getByName(Text name) {
        return counts.stream()
                .filter(count -> count.name().equals(name))
                .findFirst()
                .orElse(null);
    }
    
    public V getSum() {
        if (counts.isEmpty()) {
            return sumIfEmpty;
        }
        Assert.notNull(summer, "Summer cannot be null when values are not empty");
        return counts.stream()
                .map(LabeledCount::value)
                .reduce(summer)
                .orElse(sumIfEmpty);
    }

}
