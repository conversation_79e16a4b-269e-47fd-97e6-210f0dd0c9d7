package cz.kpsys.portaro.ext.obalkyknih;

import com.github.kagkarlsson.scheduler.task.Task;
import com.github.kagkarlsson.scheduler.task.helper.Tasks;
import com.github.kagkarlsson.scheduler.task.schedule.Schedules;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.ThisThreadAuthenticatingDynamicRunner;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdDeleter;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.obalkyknih.db.Obalkyknih;
import cz.kpsys.portaro.ext.obalkyknih.db.ObalkyknihEntity;
import cz.kpsys.portaro.ext.obalkyknih.metadata.ObalkyknihAuthorityMetadataLoader;
import cz.kpsys.portaro.ext.obalkyknih.metadata.ObalkyknihDocumentMetadataLoader;
import cz.kpsys.portaro.file.IdentifiedData;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.SpringDbIdentifiedDataSaver;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import cz.kpsys.portaro.record.file.cover.CoverDownloader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.UUID;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ObalkyknihServiceConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull Provider<@NonNull Boolean> obalkyknihUpStatusProvider;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Obalkyknih> obalkyknihSearchLoader;
    @NonNull ObalkyknihDocumentMetadataLoader obalkyknihDocumentMetadataLoader;
    @NonNull ObalkyknihAuthorityMetadataLoader obalkyknihAuthorityMetadataLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, IdentifiedFile> fileSearchLoader;
    @NonNull Provider<@NonNull User> portaroUserProvider;
    @NonNull Provider<@NonNull Integer> portaroUserIdProvider;
    @NonNull Provider<@NonNull Integer> suUserIdProvider;
    @NonNull Provider<@NonNull Integer> appserverUserIdProvider;
    @NonNull Saver<Obalkyknih, ObalkyknihEntity> obalkyknihSaver;
    @NonNull CoverDownloader obalkyknihCoverDownloader;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull ByIdDeleter<UUID> obalkyknihByIdDeleter;
    @NonNull ByIdLoadable<IdentifiedFile, Long> identifiedFileLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> recordLoader;
    @NonNull SpringDbIdentifiedDataSaver<byte[]> thumbnailCacheSaver;
    @NonNull Saver<IdentifiedData<String>, IdentifiedData<String>> identifiedFileTextDataSaver;
    @NonNull CacheDeletableById recordCache;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;
    @NonNull Saver<RecordWithAttachmentSaveCommand, IdentifiedFile> recordWithAttachmentSaver;
    @NonNull AuthenticationHolder authenticationHolder;


    @Bean
    public ObalkyknihApiController obalkyknihApiController() {
        return new ObalkyknihApiController(
                settingLoader.getOnRootProvider(ObalkyknihSettingKeys.OBALKYKNIH_ENABLED),
                obalkyknihService(),
                obalkyknihSearchLoader
        );
    }

    @Bean
    public ObalkyknihService obalkyknihService() {
        var fileModifiableChecker = new FileModifiableChecker(
                portaroUserIdProvider,
                suUserIdProvider,
                appserverUserIdProvider
        );
        return new ObalkyknihService(
                settingLoader.getOnRootProvider(ObalkyknihSettingKeys.OBALKYKNIH_ENABLED),
                settingLoader.getOnRootProvider(ObalkyknihSettingKeys.OBALKYKNIH_AS_ENABLED),
                settingLoader.getOnRootProvider(ObalkyknihSettingKeys.OBALKYKNIH_DOWNLOAD_TOC_SCAN),
                settingLoader.getOnRootProvider(ObalkyknihSettingKeys.OBALKYKNIH_DOWNLOAD_TOC_SCAN_PREVIEW),
                settingLoader.getOnRootProvider(ObalkyknihSettingKeys.OBALKYKNIH_DOWNLOAD_REFERENCES_SCAN),
                obalkyknihUpStatusProvider,
                rootDepartmentProvider,
                settingLoader.getDepartmentedFondedValuesProvider(ObalkyknihSettingKeys.OBALKYKNIH_ENABLED),
                recordLoader,
                obalkyknihDocumentMetadataLoader,
                obalkyknihAuthorityMetadataLoader,
                fileSearchLoader,
                identifiedFileLoader,
                obalkyknihSaver,
                obalkyknihByIdDeleter,
                defaultTransactionTemplateFactory.get(),
                new LinkFileUpdater(
                        obalkyknihCoverDownloader,
                        portaroUserIdProvider,
                        fileModifiableChecker,
                        thumbnailCacheSaver,
                        identifiedFileTextDataSaver
                ),
                recordCache,
                fileCategoryBySystemTypeLoader,
                recordWithAttachmentSaver,
                obalkyknihAuthRunner()
        );
    }

    @Bean
    public SpringDbNextObalkyknihItemSupplier obalkyknihItemSupplier() {
        return new SpringDbNextObalkyknihItemSupplier(
                obalkyknihSearchLoader,
                settingLoader.getOnRootProvider(ObalkyknihSettingKeys.OBALKYKNIH_RESCAN_INTERVAL)
        );
    }

    @Bean
    public ThisThreadAuthenticatingDynamicRunner obalkyknihAuthRunner() {
        return new ThisThreadAuthenticatingDynamicRunner(authenticationHolder, portaroUserProvider);
    }

    @Bean
    public Task<Void> obalkyknihTask() {
        return Tasks.recurring("obalkyknih-task", Schedules.fixedDelay(Duration.ofMinutes(1)))
                .execute((_, executionContext) -> {
                    var schedulerIsShuttingDownProvider = Provider.of(executionContext.getSchedulerState()::isShuttingDown);
                    obalkyknihService().run(obalkyknihItemSupplier(), schedulerIsShuttingDownProvider);
                });
    }

}
