package cz.kpsys.portaro.exemplar.discard;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.sequence.OptionalyPrefixedSequenceItem;
import cz.kpsys.portaro.sequence.SequenceItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.lang.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.commons.db.QueryUtils.COLSEQ;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5.*;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.UBYTKY.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FromDiscardionsDiscardNumberSequenceItemLoader implements DiscardNumberSequenceItemLoader<SequenceItem>, RowMapper<SequenceItem> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Provider<Boolean> discardNumbersWithinBuilding;


    @NonNull
    @Override
    public List<SequenceItem> get(@NonNull List<Department> deps) {
        if (discardNumbersWithinBuilding.get()) {
            return getOnParticularBuildings(deps);
        }
        return getOnWholeDatabase()
                .map(Collections::singletonList)
                .orElseGet(Collections::emptyList);
    }


    public Optional<SequenceItem> getOnWholeDatabase() {
        return getByDepartment(null);
    }


    public List<SequenceItem> getOnParticularBuildings(@NonNull List<Department> deps) {
        return deps.stream()
                .map(this::getByDepartment)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
    }


    /**
     * @param department can be null
     */
    private Optional<SequenceItem> getByDepartment(@Nullable Department department) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(UBYTKY);
        if (department != null) {
            sq.joins().add(TABLE, COLSEQ(TC(UBYTKY, ID_UBYTEK), TC(TABLE, FK_UBYTEK)));
            sq.where().and().eq(FK_PUJC, department.getId());
        }
        sq.orderBy().addDesc(TRIDUBY);
        sq.setRange(Range.forSingle());

        return ListUtil.firstOrEmptyOptional(jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this));
    }


    @Override
    public SequenceItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        String lastDiscardNumber = rs.getString(UBYT_CISLO);
        OptionalyPrefixedSequenceItem seq = OptionalyPrefixedSequenceItem.createByParsing(lastDiscardNumber);
        return seq.next();
    }

}
