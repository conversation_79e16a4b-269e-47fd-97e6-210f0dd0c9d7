package cz.kpsys.portaro.form.spring;

import cz.kpsys.portaro.commons.validation.ValidationUtils;
import jakarta.validation.ConstraintViolation;
import lombok.NonNull;

import java.util.Collection;
import java.util.function.Predicate;

public class MessageTemplateResolvingConstraintViolationExceptionRecoverableErrorResolver extends RecoverableErrorResolver<ConstraintViolation<?>> {

    public MessageTemplateResolvingConstraintViolationExceptionRecoverableErrorResolver(@NonNull Collection<Predicate<ByFormRecoverableFieldResolverParams>> customByFormRecoverableFieldResolver, @NonNull Collection<Predicate<ByFormRecoverableObjectResolverParams>> customByFormRecoverableObjectResolver) {
        super(customByFormRecoverableFieldResolver, customByFormRecoverableObjectResolver);
    }

    @Override
    protected boolean isRecoverableError(@NonNull Object violatedFormObject, ConstraintViolation<?> constraintViolation) {
        return constraintViolation.getMessageTemplate().equals(ValidationUtils.REQUIRED_INVALID_MESSAGE);
    }

}
