package cz.kpsys.portaro.loan;

import cz.kpsys.portaro.business.cancel.OverridableCanceller;
import cz.kpsys.portaro.business.consume.OverridableConsumer;
import cz.kpsys.portaro.business.function.OverridableFunction;
import cz.kpsys.portaro.business.update.CompositeSubjectUpdater;
import cz.kpsys.portaro.commons.contextual.AndBooleanContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.contextual.OverridableContextualFunction;
import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.convert.StringToIntegerToAnyConverter;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.config.ConverterRegisterer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategory;
import cz.kpsys.portaro.exemplar.thematicgroup.ThematicGroup;
import cz.kpsys.portaro.finance.AmountType;
import cz.kpsys.portaro.finance.AmountTypeLoader;
import cz.kpsys.portaro.form.valueeditor.number.NumberValueEditorModifier;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.loan.ill.*;
import cz.kpsys.portaro.loan.ill.api.SeekedSeekingProvisionEditationRequest;
import cz.kpsys.portaro.loan.ill.api.SeekingProvisionCreationRequest;
import cz.kpsys.portaro.loan.ill.persist.*;
import cz.kpsys.portaro.loan.ill.process.*;
import cz.kpsys.portaro.loan.lending.LendingService;
import cz.kpsys.portaro.loan.request.NotAnonymousLoanRequest;
import cz.kpsys.portaro.loan.resolving.LoanRulePriceResolver;
import cz.kpsys.portaro.loan.resolving.LoanRuleValueResolver;
import cz.kpsys.portaro.loan.returning.ReturningResult;
import cz.kpsys.portaro.loan.returning.ReturningService;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.payment.TransactionCreator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.SearchingByIdsLoader;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.user.Institution;
import cz.kpsys.portaro.user.Library;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import cz.kpsys.portaro.user.role.editor.LibrarianPrivileges;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

import static cz.kpsys.portaro.security.PermissionResolver.*;
import static cz.kpsys.portaro.security.PermissionResult.allow;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SeekingConfig {

    @NonNull SecurityManager securityManager;
    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NonNull LoanSetting> loanSettingProvider;
    @NonNull ContextualProvider<Department, @NonNull LoanCategory> defaultIllLoanCategoryProvider;
    @NonNull ContextualProvider<Department, @NonNull ThematicGroup> defaultIllThematicGroupProvider;
    @NonNull LoanRuleValueResolver<Integer> loanPeriodDaysResolver;
    @NonNull IdAndIdsLoadable<Loan, LoanId> nonDetailedLoanLoader;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull UserByBasicUserLoader userLoader;
    @NonNull ContextualProvider<Department, Institution> nullableInstitutionContextualProvider;
    @NonNull ReturningService returningService;
    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull ContextualProvider<Department, @NonNull Boolean> lendingToExpiredReaderAllowed;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Seeking> seekingSearchLoader;
    @NonNull Saver<Seeking, ?> seekingSaver;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull ContextualProvider<Department, @NonNull Integer> seekingIdGenerator;
    @NonNull Saver<IllSeekingProvisionList, IllSeekingProvisionList> seekingProvisionListSaver;
    @NonNull MailService mailService;
    @NonNull TemplateEngine templateEngine;
    @NonNull UserLocaleResolver userLocaleResolver;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordParagraphItemsConverter;
    @NonNull ContextualProvider<Department, @NonNull Integer> loanIdGenerator;
    @NonNull Saver<LoanEntity, LoanEntity> loanEntitySaver;
    @NonNull LendingService directLendingService;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull AmountTypeLoader amountTypeLoader;
    @NonNull LoanRulePriceResolver loanPriceResolver;
    @NonNull TransactionCreator transactionCreator;


    @Bean
    public CompositeSubjectUpdater<Seeking, Department> compositeSeekingUpdater() {
        return new CompositeSubjectUpdater<>();
    }

    @Bean
    public OverridableCanceller<SeekingSeekerCancellationCommand> seekingSeekerCanceller() {
        return new OverridableCanceller<>(
                new SeekingSeekerCanceller(
                        seekingSaver,
                        seekingActiveProvisionSeekerCanceller()
                )
        );
    }

    @Bean
    public OverridableContextualFunction<Seeking, Department, List<Library>> seekingAcceptableProvidersResolver() {
        return new OverridableContextualFunction<>(
                new SeekingProvisionCreationRequest.IllSeekingAcceptableProvidersResolver(
                        userSearchLoader,
                        departmentAccessor
                )
        );
    }

    @Bean
    public Saver<SeekedSeekingRequestCreationCommand, Seeking> seekedSeekingRequestCreator() {
        return new SeekedSeekingRequestCreator(
                seekingIdGenerator,
                nullableInstitutionContextualProvider,
                seekingSaver,
                recordEditationFactory
        );
    }

    @Bean
    public Saver<SeekedSeekingCreationCommand, Seeking> seekedSeekingCreator() {
        return new SeekedSeekingCreator(
                seekingIdGenerator,
                nullableInstitutionContextualProvider,
                seekingSaver,
                recordEditationFactory
        );
    }

    @Bean
    public Saver<ProvidedSeekingCreationCommand, Seeking> providedSeekingCreator() {
        return new ProvidedSeekingCreator(seekingIdGenerator, seekingSaver);
    }

    @Bean
    public Consumer<@NonNull SeekedSeekingEditationCommand> seekedSeekingEditor() {
        return new SeekedSeekingEditor(seekingSaver);
    }

    @Bean
    public Consumer<@NonNull SeekingCommenceCommand> seekingCommencer() {
        return new SeekingCommencer(seekingSaver);
    }

    @Bean
    public Consumer<@NonNull SeekingProvisionCreationCommand> seekingProvisionCreator() {
        return new SeekingProvisionCreator(
                seekingProvisionListSaver,
                userLoader,
                loanPriceResolver,
                defaultIllLoanCategoryProvider
        );
    }

    @Bean
    public OverridableConsumer<@NonNull SeekingProvisionsSearchStartCommand> seekingProvisionsAutoSearcher() {
        return new OverridableConsumer<>(
                new UnsupportingIllSeekingProvisionsAutoSearcher()
        );
    }

    @Bean
    public Consumer<@NonNull SeekingProvisionEditationCommand> seekingProvisionEditor() {
        return new SeekingProvisionEditor(
                seekingProvisionListSaver
        );
    }

    @Bean
    public Consumer<@NonNull SeekingProvisionCancellationCommand> seekingProvisionCanceller() {
        return new SeekingProvisionCanceller(
                seekingProvisionListSaver
        );
    }

    @Bean
    public Saver<SeekedSeekingActiveProvisionEditationCommand, ?> seekedSeekingActiveProvisionEditor() {
        return new SeekedSeekingActiveProvisionEditor(
                seekingSaver
        );
    }

    @Bean
    public Saver<ProvidedSeekingActiveProvisionEditationCommand, ?> providedSeekingActiveProvisionEditor() {
        return new ProvidedSeekingActiveProvisionEditor(
                seekingSaver
        );
    }

    @Bean
    public NumberValueEditorModifier<SeekedSeekingProvisionEditationRequest> seekedSeekingProvisionEditationRequestOrderEditorModifier() {
        return new SeekedSeekingProvisionEditationRequest.SeekedSeekingProvisionEditationRequestOrderEditorModifier(
                seekingLoader()
        );
    }

    @Bean
    public OverridableConsumer<@NonNull SeekingUpcomingProvisionActivateCommand> seekingUpcomingProvisionActivator() {
        return new OverridableConsumer<>(
                new SeekingUpcomingProvisionActivator(
                        seekingSaver,
                        nullableInstitutionContextualProvider,
                        userLoader,
                        providedSeekingCreator()
                )
        );
    }

    @Bean
    public OverridableConsumer<@NonNull SeekingUpcomingProvisionMessageSendCommand> seekingActiveProvisionMessageSender() {
        return new OverridableConsumer<>(
                new SeekingActiveProvisionMessageSender(
                        mailService,
                        templateEngine,
                        userLocaleResolver,
                        recordsToViewableRecordParagraphItemsConverter
                )
        );
    }

    @Bean
    public OverridableConsumer<@NonNull SeekingActiveProvisionAcceptCommand> seekingActiveProvisionAcceptor() {
        return new OverridableConsumer<>(
                new SeekingActiveProvisionAcceptor(seekingSaver, localizer)
        );
    }

    @Bean
    public OverridableConsumer<@NonNull SeekingActiveProvisionConditionAcceptCommand> seekingActiveProvisionConditionAcceptor() {
        return new OverridableConsumer<>(
                new SeekingActiveProvisionConditionAcceptor(seekingSaver)
        );
    }

    @Bean
    public OverridableConsumer<@NonNull SeekingActiveProvisionExemplarSendCommand> seekingActiveProvisionExemplarSender() {
        return new OverridableConsumer<>(
                standardSeekingActiveProvisionExemplarSender()
        );
    }

    @Bean
    public SeekingActiveProvisionExemplarSender standardSeekingActiveProvisionExemplarSender() {
        return new SeekingActiveProvisionExemplarSender(
                seekingSaver,
                directLendingService
        );
    }

    @Bean
    public OverridableConsumer<@NonNull SeekingActiveProvisionExemplarReceiveCommand> seekingActiveProvisionExemplarReceiver() {
        return new OverridableConsumer<>(
                standardSeekingActiveProvisionExemplarReceiver()
        );
    }

    @Bean
    public SeekingActiveProvisionExemplarReceiver standardSeekingActiveProvisionExemplarReceiver() {
        return new SeekingActiveProvisionExemplarReceiver(
                seekingSaver,
                loanIdGenerator,
                loanEntitySaver,
                nonDetailedLoanLoader,
                defaultIllLoanCategoryProvider,
                defaultIllThematicGroupProvider,
                loanPeriodDaysResolver,
                userLoader,
                mvsAmountTypeProvider(),
                transactionCreator
        );
    }

    @Bean
    public OverridableFunction<@NonNull SeekingActiveProvisionExemplarSendBackCommand, @NonNull ReturningResult> seekingActiveProvisionExemplarBackSender() {
        return new OverridableFunction<>(
                standardSeekingActiveProvisionExemplarBackSender()
        );
    }

    @Bean
    public SeekingActiveProvisionExemplarBackSender standardSeekingActiveProvisionExemplarBackSender() {
        return new SeekingActiveProvisionExemplarBackSender(
                seekingSaver,
                returningService,
                nonDetailedLoanLoader
        );
    }

    @Bean
    public OverridableFunction<@NonNull SeekingActiveProvisionExemplarReceiveBackCommand, @NonNull ReturningResult> seekingActiveProvisionExemplarBackReceiver() {
        return new OverridableFunction<>(
                standardSeekingActiveProvisionExemplarBackReceiver()
        );
    }

    @Bean
    public SeekingActiveProvisionExemplarBackReceiver standardSeekingActiveProvisionExemplarBackReceiver() {
        return new SeekingActiveProvisionExemplarBackReceiver(
                seekingSaver,
                returningService,
                nonDetailedLoanLoader
        );
    }

    @Bean
    public OverridableCanceller<SeekingActiveProvisionSeekerCancelCommand> seekingActiveProvisionSeekerCanceller() {
        return new OverridableCanceller<>(
                new SeekingActiveProvisionSeekerCanceller(seekingSaver)
        );
    }

    @Bean
    public OverridableCanceller<SeekingActiveProvisionProviderCancelCommand> seekingActiveProvisionProviderCanceller() {
        return new OverridableCanceller<>(
                new SeekingActiveProvisionProviderCanceller(seekingSaver)
        );
    }

    @Bean
    public Codebook<SeekingState, String> seekingStateLoader() {
        return SeekingState.CODEBOOK;
    }

    @Bean
    public IllSeekingsLoader seekingsLoader() {
        return new TransactionalIllSeekingsLoader(
                new ConditionalIllSeekingsLoader(
                        loanSettingProvider,
                        new SecuredIllSeekingsLoader(
                                securityManager,
                                new SearchingIllSeekingsLoader(seekingSearchLoader, departmentAccessor)
                        )
                )
        );
    }

    @Bean
    public IdAndIdsLoadable<Seeking, Integer> seekingLoader() {
        Function<List<Integer>, Consumer<MapBackedParams>> idsToParamsFunction = ids -> StaticParamsModifier.of(
                SeekingConstants.SearchParams.INCLUDE_SEEKERS_VIEW, true,
                SeekingConstants.SearchParams.INCLUDE_PROVIDERS_VIEW, true,
                SeekingConstants.SearchParams.SEEKING_ID, ids
        );
        AllByIdsLoadable<Seeking, Integer> searchingByIdsLoader = new SearchingByIdsLoader<>(idsToParamsFunction, seekingSearchLoader);
        return new ByIdLoadableByAllByIdsLoadable<>(searchingByIdsLoader, Seeking.class);
    }

    @Bean
    public CommandLineRunner seekingConvertersRegistrar() {
        return args -> {
            converterRegisterer.registerForIntegerIdWithCustomFromStringConversion(Seeking.class, seekingLoader(), StringToIntegerToAnyConverter.strict(new IdToObjectConverter<>(seekingLoader()))); // aby fungovaly ciselna idecka v json stringu (to pouzivame abychom mohli v budoucnu prejit na uuid)
            converterRegisterer.registerForStringId(SeekingState.class, seekingStateLoader());
        };
    }

    @Bean
    public ContextualProvider<Department, @NonNull Boolean> passiveIllEnabledProvider() {
        return AndBooleanContextualProvider.of(
                ctx -> loanSettingProvider.getOn(ctx).isEnabled(),
                settingLoader.getDepartmentedProvider(IllSettingKeys.MVS_PASSIVE_ENABLED)
        );
    }

    @Bean
    public ContextualProvider<Department, @NonNull AmountType> mvsAmountTypeProvider() {
        return settingLoader.getDepartmentedProvider(IllSettingKeys.MVS_AMOUNT_TYPE_ID)
                .andThen(amountTypeLoader::getById);
    }

    @Bean
    public CommandLineRunner seekingPermissionsRegistrar() {
        return args -> {
            permissionRegistry.add(IllLoanSecurityActions.SEEKING_SEARCH, permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_SEARCH));

            permissionRegistry.add(IllLoanSecurityActions.SEEKING_REQUEST_CREATE_OF_REQUESTER, and(
                    permissionFactory.enabled(passiveIllEnabledProvider(), Texts.ofNative("Funkce passivních MVS není zapnuta")),
                    adaptingSubject(
                            userLoader::getUser,
                            and(
                                    (auth, ctx, requester) -> !(requester instanceof Library) ? allow() : PermissionResult.forbid(), //#12607 pasivni mvs nechceme zobrazovat knihovnam - toto budeme chtit nejak resit, abychom pouze povolovali podle roli
                                    permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                                    or(
                                            permissionFactory.currentEvidedAuthenticActiveIsSubjectUser(),
                                            permissionFactory.currentEvidedAuthenticEditWithLoanLicence()
                                    )
                            )
                    ))
            );

            permissionRegistry.add(IllLoanSecurityActions.SEEKING_REQUEST_CREATE, adaptingSubject(
                    NotAnonymousLoanRequest::getRequester,
                    permissionRegistry.getLazy(IllLoanSecurityActions.SEEKING_REQUEST_CREATE_OF_REQUESTER)
            ));

            permissionRegistry.add(IllLoanSecurityActions.SEEKING_CREATE, and(
                    permissionFactory.enabled(passiveIllEnabledProvider(), Texts.ofNative("Funkce passivních MVS není zapnuta")),
                    adaptingSubject(
                            SeekedSeekingCreationCommand::requester,
                            and(
                                    permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                                    permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_CREATE)
                            )
                    ))
            );

            permissionRegistry.add(IllLoanSecurityActions.SEEKED_SEEKING_PROCESS, adaptingSubject(
                    seeking -> userLoader.getUser(seeking.knownRequester()),
                    and(
                            permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                            permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_EDIT)
                    )
            ));

            permissionRegistry.add(IllLoanSecurityActions.SEEKED_SEEKING_RECEIVE, adaptingSubject(
                    seeking -> userLoader.getUser(seeking.knownRequester()),
                    and(
                            permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                            permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_SEND_OR_RECEIVE)
                    )
            ));

            permissionRegistry.add(IllLoanSecurityActions.SEEKED_SEEKING_SEND_BACK, adaptingSubject(
                    seeking -> userLoader.getUser(seeking.knownRequester()),
                    and(
                            permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                            permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_SEND_BACK_OR_RECEIVE_BACK)
                    )
            ));

            permissionRegistry.add(IllLoanSecurityActions.PROVIDED_SEEKING_CREATE, adaptingSubject(
                    ProvidedSeekingCreationCommand::seeker,
                    and(
                            permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                            permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_CREATE)
                    )
            ));

            permissionRegistry.add(IllLoanSecurityActions.PROVIDED_SEEKING_PROCESS, adaptingSubject(
                    seeking -> userLoader.getUser(seeking.knownSeeker()),
                    and(
                            permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                            permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_EDIT)
                    )
            ));

            permissionRegistry.add(IllLoanSecurityActions.PROVIDED_SEEKING_SEND, adaptingSubject(
                    seeking -> userLoader.getUser(seeking.knownSeeker()),
                    and(
                            permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                            permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_SEND_OR_RECEIVE)
                    )
            ));

            permissionRegistry.add(IllLoanSecurityActions.PROVIDED_SEEKING_RECEIVE_BACK, adaptingSubject(
                    seeking -> userLoader.getUser(seeking.knownSeeker()),
                    and(
                            permissionFactory.subjectUserIsEvidedActiveUnblockedReaderWithLendingAllowedAndActiveRegistration(lendingToExpiredReaderAllowed),
                            permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_SEND_BACK_OR_RECEIVE_BACK)
                    )
            ));

            permissionRegistry.add(IllLoanSecurityActions.SEEKING_CANCEL, permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_LOANS_MVS_STORNO));
        };
    }

}
