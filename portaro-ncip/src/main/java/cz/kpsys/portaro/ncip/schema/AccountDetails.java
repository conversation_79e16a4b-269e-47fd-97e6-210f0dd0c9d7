
package cz.kpsys.portaro.ncip.schema;

import jakarta.xml.bind.annotation.*;

import javax.annotation.Generated;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}AccrualDate"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}FiscalTransactionInformation"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Ext" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "accrualDate",
    "fiscalTransactionInformation",
    "ext"
})
@XmlRootElement(name = "AccountDetails")
@Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
public class AccountDetails {

    @XmlElement(name = "AccrualDate", required = true)
    @XmlSchemaType(name = "dateTime")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected XMLGregorianCalendar accrualDate;
    @XmlElement(name = "FiscalTransactionInformation", required = true)
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected FiscalTransactionInformation fiscalTransactionInformation;
    @XmlElement(name = "Ext")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected Ext ext;

    /**
     * Gets the value of the accrualDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public XMLGregorianCalendar getAccrualDate() {
        return accrualDate;
    }

    /**
     * Sets the value of the accrualDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setAccrualDate(XMLGregorianCalendar value) {
        this.accrualDate = value;
    }

    /**
     * Gets the value of the fiscalTransactionInformation property.
     * 
     * @return
     *     possible object is
     *     {@link FiscalTransactionInformation }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public FiscalTransactionInformation getFiscalTransactionInformation() {
        return fiscalTransactionInformation;
    }

    /**
     * Sets the value of the fiscalTransactionInformation property.
     * 
     * @param value
     *     allowed object is
     *     {@link FiscalTransactionInformation }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setFiscalTransactionInformation(FiscalTransactionInformation value) {
        this.fiscalTransactionInformation = value;
    }

    /**
     * Gets the value of the ext property.
     * 
     * @return
     *     possible object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public Ext getExt() {
        return ext;
    }

    /**
     * Sets the value of the ext property.
     * 
     * @param value
     *     allowed object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setExt(Ext value) {
        this.ext = value;
    }

}
