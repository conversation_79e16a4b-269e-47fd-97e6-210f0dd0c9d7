<script lang="ts">
    import {createEventDispatcher} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    export let printMode = false;

    const dispatch = createEventDispatcher<{'print': void}>();
    const localize = getLocalization();

    const handlePrintClick = () => {
        dispatch('print');
    };
</script>

<div class="public-stats-heading-container">
    <KpHeading type="h1">
        <slot/>
    </KpHeading>

    {#if !printMode}
        <div class="print-button-container">
            <KpButton on:click={handlePrintClick}>
                <IconedContent icon="print">
                    {localize(/* @kp-localization commons.tisk */ 'commons.tisk')}
                </IconedContent>
            </KpButton>

            <small class="text-muted">
                {localize(/* @kp-localization statistiky.PrintLabel */ 'statistiky.PrintLabel')}
            </small>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .public-stats-heading-container {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: end;

        .print-button-container {
            display: flex;
            flex-direction: column;
            align-items: end;
            gap: @spacing-s;

            small {
                text-wrap: nowrap;
            }
        }
    }
</style>