package cz.kpsys.portaro.commons.concurrent;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Semaphore;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SemaphoreLocker implements GlobalLocker {

    @NonNull Semaphore semaphore;

    public SemaphoreLocker(int maxNofThreads) {
        semaphore = new Semaphore(maxNofThreads);
    }

    @SneakyThrows
    @Override
    public <R> R lock(Supplier<R> action) {
        semaphore.acquire();
        try {
            return action.get();
        } finally {
            semaphore.release();
        }
    }
}
