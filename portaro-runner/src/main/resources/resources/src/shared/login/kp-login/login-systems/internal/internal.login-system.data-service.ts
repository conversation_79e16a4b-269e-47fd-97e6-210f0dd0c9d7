import type {AjaxService} from 'core/data-services/ajax.service';
import type {Auth} from 'typings/portaro.be.types';
import type {InternalLoginCredentials} from 'shared/login/kp-login/login-systems/internal/types';

export class InternalLoginSystemDataService {
    public static serviceName = 'internalLoginSystemDataService'


    private static readonly INTERNAL_AUTH_ROUTE = '/auth/internal';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public sendRequest(credentials: InternalLoginCredentials): Promise<Auth> {
        return this.ajaxService
            .createRequest(InternalLoginSystemDataService.INTERNAL_AUTH_ROUTE)
            .post<Auth>(credentials);
    }
}