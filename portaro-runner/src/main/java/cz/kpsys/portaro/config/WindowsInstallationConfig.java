package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.io.HttpFileTransferer;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.licence.BusinessVersion;
import cz.kpsys.portaro.monitoring.health.StaticSettableHealthIndicator;
import cz.kpsys.portaro.resourcesupdate.JsonFileCurrentVersionSettableProvider;
import cz.kpsys.portaro.resourcesupdate.Portaro20FilesystemUpdatesProvider;
import cz.kpsys.portaro.resourcesupdate.ResourcesUpdaterImpl;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.HttpClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.concurrent.TimeUnit;

import static org.apache.commons.lang3.SystemUtils.IS_OS_WINDOWS;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class WindowsInstallationConfig {

    @NonNull StaticSettableHealthIndicator filesystemStructureHealthIndicator;
    @NonNull HttpClient fileDataHttpClient;
    @NonNull Provider<@NonNull BusinessVersion> licenceVersionProvider;

    @Scheduled(initialDelay = 10, fixedDelay = 99999999, timeUnit = TimeUnit.SECONDS)
    public void updateWindowsFilesystemResources() {
        try {
            if (IS_OS_WINDOWS) {
                JsonFileCurrentVersionSettableProvider versionRepository = new JsonFileCurrentVersionSettableProvider(new FileSystemResource("bin/version.json"), "portaro_2_0");
                Portaro20FilesystemUpdatesProvider portaro20Updates = new Portaro20FilesystemUpdatesProvider(new HttpFileTransferer(fileDataHttpClient), licenceVersionProvider);
                ResourcesUpdaterImpl updater20 = new ResourcesUpdaterImpl(versionRepository, portaro20Updates, true);
                updater20.update();
            }
            filesystemStructureHealthIndicator.setUp();
        } catch (Exception e) {
            filesystemStructureHealthIndicator.setDown(e);
            throw e;
        }
    }
}
