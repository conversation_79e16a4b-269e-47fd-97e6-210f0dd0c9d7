package cz.kpsys.portaro.payment;

import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class TransactionsRefunder {

    @NonNull Saver<List<Transaction>, ?> transactionsSaver;
    @NonNull TransactionLoader transactionLoader;
    @NonNull TransactionTemplate readwriteTransactionTemplate;

    public void processTransactionsRefund(@NonNull Payment payment) {
        readwriteTransactionTemplate.executeWithoutResult(_ -> {
            refund(payment);
        });
    }

    private void refund(@NonNull Payment payment) {
        List<Transaction> transactions = transactionLoader.getAllByPayment(payment.getId());
        List<Transaction> updatedTransactions = transactions.stream()
                .filter(transaction -> transaction.needsRefundUpdate(payment.getRefundDate(), payment.getId()))
                .peek(transaction -> {
                    transaction.setRefundDate(payment.getRefundDate());
                    transaction.setRefundPaymentId(payment.getId());
                })
                .toList();

        if (!updatedTransactions.isEmpty()) {
            transactionsSaver.save(updatedTransactions);
        }
    }
}
