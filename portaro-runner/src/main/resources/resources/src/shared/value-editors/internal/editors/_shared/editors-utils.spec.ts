import {emptyAsNullFormatter, emptyAsNullParser, trim} from './editors-utils';

describe('value editors common utils', () => {
    describe('emptyAsNullParser', () => {
        it('should return same value if emptyAsNull is false', () => {
            const parser = emptyAsNullParser({emptyAsNull: false});

            expect(parser('')).toEqual('');
            expect(parser('abc')).toEqual('abc');
        });

        it('should return same value if not empty', () => {
            const parser = emptyAsNullParser({emptyAsNull: true});
            expect(parser('abc')).toEqual('abc');
        });

        it('should return null if value is empty', () => {
            const parser = emptyAsNullParser({emptyAsNull: true});
            expect(parser('')).toEqual(null);
        });

        it('should support customEmptyAsNullCheck option', () => {
            const parser = emptyAsNullParser({
                emptyAsNull: true,
                customEmptyAsNullCheck: ({value}) => value === 'XXX'});

            expect(parser('XXX')).toEqual(null);
        });

        it('should work with arrays', () => {
            const parser = emptyAsNullParser({emptyAsNull: true});
            expect(parser([])).toEqual(null);
        });
    });

    describe('emptyAsNullFormatter', () => {
        it('should return same value if emptyAsNull is false', () => {
            const formatter = emptyAsNullFormatter({emptyAsNull: false});

            expect(formatter('')).toEqual('');
            expect(formatter(null)).toEqual(null);
            expect(formatter('abc')).toEqual('abc');
        });

        it('should return same value if not empty', () => {
            const formatter = emptyAsNullFormatter({emptyAsNull: true});
            expect(formatter('abc')).toEqual('abc');
        });

        it('should return null if value is empty', () => {
            const formatter = emptyAsNullFormatter({emptyAsNull: true});
            expect(formatter(null)).toEqual('');
        });

        it('should work with arrays', () => {
            const formatter = emptyAsNullFormatter({emptyAsNull: true}, []);
            expect(formatter(null)).toEqual([]);
        });
    });

    describe('trim', () => {
        it('should return same value if trim option is false', () => {
            const trimmer = trim({trim: false});
            expect(trimmer('')).toEqual('');
            expect(trimmer(' abc')).toEqual(' abc');
            expect(trimmer('abc ')).toEqual('abc ');
            expect(trimmer(' abc ')).toEqual(' abc ');
        });

        it('should return trim input values', () => {
            const trimmer = trim({trim: true});
            expect(trimmer('')).toEqual('');
            expect(trimmer(' abc')).toEqual('abc');
            expect(trimmer('abc ')).toEqual('abc');
            expect(trimmer(' abc ')).toEqual('abc');
        });
    });
})