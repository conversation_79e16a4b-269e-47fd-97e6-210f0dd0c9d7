package cz.kpsys.portaro.sql.generator;

import cz.kpsys.portaro.databaseproperties.DatabaseConnectionSettings;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SqlDataClearHelper {

    @NonNull DatabaseProperties dbUpdateDatabaseProperties;

    public QueryFactory getQueryFactoryByDatabaseType() {
        if (dbUpdateDatabaseProperties.getType().equals(DatabaseConnectionSettings.DBTYPE_FIREBIRD)) {
            return new QueryFactory(new DbSpecificsFirebird());
        } else {
            return new QueryFactory(new DbSpecificsPostgresql());
        }
    }

    public Query deleteTable(QueryFactory qf, @NonNull String tableName) {
        DeleteQuery sq = qf.newDeleteQuery();
        sq.delete(tableName);
        return sq;
    }

    public Query deleteTableWithExcludedIds(QueryFactory qf, @NonNull String tableName, @NonNull String columnName, @NonNull List<?> values) {
        DeleteQuery sq = qf.newDeleteQuery();
        sq.delete(tableName);
        sq.where().and().notIn(columnName, values);
        return sq;
    }

    public static Query deleteFromTableByColumnIn(QueryFactory qf, @NonNull String tableName, @NonNull String columnName, @NonNull List<UUID> values) {
        DeleteQuery sq = qf.newDeleteQuery();
        sq.delete(tableName);
        sq.where().in(columnName, values);
        return sq;
    }

    public static Query deleteFromTableByListOfIds(QueryFactory qf, @NonNull String tableName, @NonNull String columnName, @NonNull List<?> values) {
        DeleteQuery sq = qf.newDeleteQuery();
        sq.delete(tableName);
        sq.where().and().in(columnName, values);
        return sq;
    }

    public Query updateTable(QueryFactory qf, @NonNull String table, @NonNull String updateColumn, String updateValue, @NonNull String whereColumn, List<Long> whereValues) {
        UpdateQuery uq = qf.newUpdateQuery();
        uq.update(table);
        uq.set().add(updateColumn, updateValue);
        uq.where().and().notIn(whereColumn, whereValues);
        return uq;
    }

    public Query updateTableSetNullWhereNotNull(QueryFactory qf, @NonNull String table, @NonNull String updateColumn, @NonNull String whereColumn) {
        UpdateQuery uq = qf.newUpdateQuery();
        uq.update(table);
        uq.set().add(updateColumn, null);
        uq.where().isNotNull(whereColumn);
        return uq;
    }

    public static Query updateColumnLike(QueryFactory qf, @NonNull String table, @NonNull String updateColumn, String updateValue, @NonNull String whereColumn, @NonNull List<UUID> values) {
        UpdateQuery uq = qf.newUpdateQuery();
        uq.update(table);
        uq.set().add(updateColumn, updateValue);
        Brackets brackets = uq.where().and().brackets();
        for (UUID recordId : values) {
            brackets.or().like(TC(table, whereColumn), recordId.toString(), true, true);
        }
        return uq;
    }

    public static Query updateColumnEq(QueryFactory qf, @NonNull String table, @NonNull String updateColumn, String updateValue, @NonNull String whereColumn, @NonNull List<UUID> values) {
        UpdateQuery uq = qf.newUpdateQuery();
        uq.update(table);
        uq.set().add(updateColumn, updateValue);
        Brackets brackets = uq.where().and().brackets();
        for (UUID recordId : values) {
            brackets.or().eq(TC(table, whereColumn), recordId.toString());
        }
        return uq;
    }

}
