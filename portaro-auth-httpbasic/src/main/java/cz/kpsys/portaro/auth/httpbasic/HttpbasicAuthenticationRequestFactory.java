package cz.kpsys.portaro.auth.httpbasic;

import cz.kpsys.portaro.auth.process.AuthenticationRequestFactory;
import cz.kpsys.portaro.auth.stringpair.UsernamePasswordPair;
import cz.kpsys.portaro.department.Department;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class HttpbasicAuthenticationRequestFactory implements AuthenticationRequestFactory<UsernamePasswordPair, HttpbasicAuthenticationRequest> {

    @NonNull AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource = new WebAuthenticationDetailsSource();

    @Override
    public HttpbasicAuthenticationRequest createAuthenticationRequest(@NonNull UsernamePasswordPair token, @NonNull HttpServletRequest request, @NonNull Department department) {
        HttpbasicAuthenticationRequest authRequest = new HttpbasicAuthenticationRequest(department, token.username(), token.password());
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
        return authRequest;
    }
}
