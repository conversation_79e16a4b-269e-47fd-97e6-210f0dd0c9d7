package cz.kpsys.portaro.appserver;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.ConnectTimeoutException;
import org.apache.hc.client5.http.HttpHostConnectException;
import org.jdom2.Document;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestOperations;

import java.net.SocketTimeoutException;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
@ToString(onlyExplicitlyIncluded = true)
public class BasicAppserverService implements AppserverService {

    @ToString.Include @NonNull String appserverUrl;
    @NonNull RestOperations rest;
    @NonNull ObjectMapper xmlMapper;
    @NonNull XmlFormatter xmlFormatter = new XmlFormatter();


    @Override
    public <T> T call(AppserverRequest<?> request, Class<T> requiredType) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException {
        String responseXml = callAndGetXmlString(request);

        try {
            return xmlMapper.readValue(responseXml, requiredType);
        } catch (Exception e) {
            throw new AppserverResponseParsingException(request, responseXml, e);
        }
    }


    @Override
    public Document call(AppserverRequest<?> request) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException {
        String responseXml = callAndGetXmlString(request);

        try {
            return xmlFormatter.parseXml(responseXml);
        } catch (Exception e) {
            throw new AppserverResponseParsingException(request, responseXml, e);
        }
    }


    public String callAndGetXmlString(AppserverRequest<?> request) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException {

        String url = appserverUrl + request.getPathWithParams();
        ResponseEntity<String> responseEntity;
        TimeMeter tm = TimeMeter.start();


        try {
            HttpMethod method = request.getMethod();
            if (HttpMethod.GET.equals(method)) {
                responseEntity = rest.getForEntity(url, String.class);

            } else if (HttpMethod.POST.equals(method)) {
                MultiValueMap<String, Object> requestData = new LinkedMultiValueMap<>();
                requestData.add("data", request.getBody());
                if (ListUtil.hasLength(request.getFiles())) {
                    for (Map.Entry<String, Resource> fileEntry : request.getFiles().entrySet()) {
                        HttpHeaders fileHeaders = new HttpHeaders();
                        fileHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                        requestData.add(fileEntry.getKey(), new HttpEntity<Object>(fileEntry.getValue(), fileHeaders));
                    }
                }
                HttpEntity<?> httpEntity = new HttpEntity<Object>(requestData, request.getHeaders());
                responseEntity = rest.postForEntity(url, httpEntity, String.class);

            } else {
                throw new UnsupportedOperationException("Request method %s is not supported".formatted(request.getMethod()));
            }

        } catch (ResourceAccessException t) {
            if (t.getCause() instanceof HttpHostConnectException || t.getCause() instanceof ConnectTimeoutException) { //toto jsou dve standardni chyby pri nejedoucim AS
                throw new AppserverAccessException(request, tm.elapsedTimeString(), t);
            }
            if (t.getCause() instanceof SocketTimeoutException) { // toto je chyba, kdy RestTemplate usoudi, ze se ceka na odpoved moc dlouho (typicky, kdyz aplikacnimu serveru trva dlouho recons)
                throw new AppserverResponseTimeoutException(request, tm.elapsedTimeString(), t);
            }
            throw t;

        } catch (RestClientResponseException t) {
            HttpStatusCode statusCode = t.getStatusCode();
            String responseBody = t.getResponseBodyAsString();
            throw new AppserverTransferException(request, statusCode, responseBody, tm.elapsedTimeString(), t);
        }


        String elapsedTimeString = tm.elapsedTimeString();
        HttpStatusCode responseStatus = responseEntity.getStatusCode();
        String responseBody = StringUtil.trimOrLetNull(responseEntity.getBody());

        if (log.isInfoEnabled()) {
            log.info(AppserverUtils.createCallInfo(request, responseStatus, responseBody, elapsedTimeString));
        }

        //osetreni toho, kdyz appserver vrati ne-xml
        if (StringUtil.isNullOrBlank(responseBody) || !responseBody.trim().startsWith("<")) {
            responseBody = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><collection>" + responseBody + "</collection>";
        }

        return responseBody;
    }

}
