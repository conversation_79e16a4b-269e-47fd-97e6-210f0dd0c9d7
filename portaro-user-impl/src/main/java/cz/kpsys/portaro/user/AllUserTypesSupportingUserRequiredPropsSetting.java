package cz.kpsys.portaro.user;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.contact.ContactManager;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AllUserTypesSupportingUserRequiredPropsSetting implements UserRequiredPropsSetting {

    @NonNull ContextualProvider<Department, List<String>> personBySelfRequiredProps;
    @NonNull ContextualProvider<Department, List<String>> personByEditorRequiredProps;
    @NonNull List<String> supplierSelfRequiredProps;
    @NonNull List<String> institutionRequiredProps;
    @NonNull List<String> softwareRequiredProps;
    @NonNull ContactManager contactManager;
    @NonNull ExternalUserResolver externalUserResolver;

    @Override
    public Set<String> getRequiredProps(User user, UserAuthentication currentAuth, Department ctx) {
        Set<String> allRequiredProps = new HashSet<>();

        if (user.hasRole(ReaderRole.class)) {
            allRequiredProps.addAll(getReaderUserRequiredProps(user, currentAuth, ctx));
        }

        if (user.hasRole(EditorAccount.class)) {
            allRequiredProps.addAll(getEditorUserRequiredProps(user, currentAuth, ctx));
        }

        if (user.hasRole(SupplierRole.class)) {
            allRequiredProps.addAll(getSupplierUserRequiredProps(user, currentAuth, ctx));
        }

        if (user instanceof Institution) {
            allRequiredProps.addAll(getInstitutionRequiredProps((Institution) user, currentAuth, ctx));
        }

        if (user instanceof Software) {
            allRequiredProps.addAll(getSoftwareRequiredProps((Software) user, currentAuth, ctx));
        }

        allRequiredProps = removeCredentialsIfExternalLogin(user, allRequiredProps, ctx);

        allRequiredProps = removePasswordIfExists(user, allRequiredProps, ctx);

        removeEmailIfInGroup(user, allRequiredProps);

        return allRequiredProps;
    }

    private Set<String> getReaderUserRequiredProps(User user, UserAuthentication currentAuth, Department currentDepartment) {
        if (user instanceof Person) {
            if (isEditorOnCurrentDepartment(currentAuth, currentDepartment)) {
                return new HashSet<>(personByEditorRequiredProps.getOn(currentDepartment));
            }

            return new HashSet<>(personBySelfRequiredProps.getOn(currentDepartment));
        }

        return Stream.of(UserEditableFields.NAME).collect(Collectors.toSet());
    }

    private Set<String> getEditorUserRequiredProps(User user, UserAuthentication currentAuth, Department currentDepartment) {
        if (user instanceof Person) {
            if (isEditorOnCurrentDepartment(currentAuth, currentDepartment)) {
                return personByEditorRequiredProps.getOn(currentDepartment).stream()
                        .filter(s -> Set.of(UserEditableFields.USERNAME, UserEditableFields.EMAIL).contains(s))
                        .collect(Collectors.toSet());
            }

            return personBySelfRequiredProps.getOn(currentDepartment).stream()
                    .filter(s -> Set.of(UserEditableFields.USERNAME, UserEditableFields.PASSWORD, UserEditableFields.EMAIL).contains(s))
                    .collect(Collectors.toSet());
        }
        return Stream.of(UserEditableFields.NAME).collect(Collectors.toSet());
    }

    private Set<String> getSupplierUserRequiredProps(User user, UserAuthentication currentAuth, Department currentDepartment) {
        return new HashSet<>(supplierSelfRequiredProps);
    }

    private Set<String> getInstitutionRequiredProps(Institution user, UserAuthentication currentAuth, Department currentDepartment) {
        return new HashSet<>(institutionRequiredProps);
    }

    private Set<String> getSoftwareRequiredProps(Software user, UserAuthentication currentAuth, Department currentDepartment) {
        return new HashSet<>(softwareRequiredProps);
    }

    private boolean isEditorOnCurrentDepartment(UserAuthentication currentAuth, Department currentDepartment) {
        return currentAuth.getActiveUser().hasRoleOn(EditorAccount.class, currentDepartment);
    }

    private Set<String> removePasswordIfExists(User user, Set<String> set, Department currentDepartment) {
        Set<String> result = new HashSet<>(set);
        if (user.getPasswordHash() != null) {
            result.remove(UserEditableFields.PASSWORD);
        }
        return result;
    }

    private Set<String> removeCredentialsIfExternalLogin(User user, Set<String> set, Department ctx) {
        Set<String> result = new HashSet<>(set);
        if (externalUserResolver.isExternalUser(user, ctx)) {
            result.remove(UserEditableFields.USERNAME);
            result.remove(UserEditableFields.PASSWORD);
        }

        return result;
    }

    private void removeEmailIfInGroup(User user, Set<String> allRequiredProps) {
        if (contactManager.getFallbackEmail(user).isPresent()) {
            allRequiredProps.remove(UserEditableFields.EMAIL);
        }
    }
}
