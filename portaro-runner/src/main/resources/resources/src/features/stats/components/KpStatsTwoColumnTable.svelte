<script lang="ts">
    import type {TwoColumnTableData} from '../types';
    import {exists} from 'shared/utils/custom-utils';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpTitledSection from 'shared/layouts/containers/KpTitledSection.svelte';

    export let title: string;
    export let tableData: TwoColumnTableData;
    export let centeredValue = false;

    $: headerExists = exists(tableData.firstColumnTitle) && exists(tableData.secondColumnTitle);
    $: footerExists = exists(tableData.footer);
</script>

<KpTitledSection {title}
                 additionalClasses="kp-stats-two-column-table-container"
                 headingType="h3">

    <KpClassicTable
            additionalClasses="two-column-table {footerExists ? '' : 'without-footer'} {headerExists ? '' : 'without-header'}"
            hoverRows
            colorAccented
            horizontallyDivided
            columnHeadersCentered>

        <tr slot="header">
            {#if headerExists}
                <th>{tableData.firstColumnTitle}</th>
                <th>{tableData.secondColumnTitle}</th>
            {/if}
        </tr>

        <svelte:fragment slot="body">
            {#each tableData.rows as row, index}
                <tr>
                    <td class:first-td={index === 0}>{row.label}</td>
                    <td class:centered-value-column={centeredValue} class:first-td={index === 0}>{row.value}</td>
                </tr>
            {/each}
        </svelte:fragment>

        <tr slot="footer">
            {#if footerExists}
                <th>{tableData.footer.label}</th>
                <th class:centered-value-column={centeredValue}>{tableData.footer.value}</th>
            {/if}
        </tr>
    </KpClassicTable>
</KpTitledSection>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .kp-stats-two-column-table-container .table-container {
            border: 1px solid @themed-border-default;
            overflow: hidden;
            border-radius: @border-radius-default;

            .two-column-table {
                border-radius: @border-radius-default;

                &.without-header thead {
                    display: none;
                }

                &.without-footer tfoot {
                    display: none;
                }

                .first-td {
                    border-top: none !important;
                }

                .centered-value-column {
                    text-align: center;
                }
            }
        }
    }
</style>