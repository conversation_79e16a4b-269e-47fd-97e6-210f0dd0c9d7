package cz.kpsys.portaro.commons.web;

import cz.kpsys.portaro.commons.object.Provider;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ConditionalWebResolver<V> implements WebResolver<V> {

    @NonNull WebResolver<Boolean> enabledWebResolver;
    @NonNull Provider<@NonNull V> valueProvider;

    @Override
    public Optional<V> tryResolve(@NonNull HttpServletRequest request) {
        if (enabledWebResolver.resolve(request)) {
            return Optional.of(valueProvider.get());
        }
        return Optional.empty();
    }
}
