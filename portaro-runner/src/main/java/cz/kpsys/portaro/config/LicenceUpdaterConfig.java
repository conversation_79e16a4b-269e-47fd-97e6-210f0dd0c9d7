package cz.kpsys.portaro.config;

import com.github.kagkarlsson.scheduler.task.Task;
import com.github.kagkarlsson.scheduler.task.helper.Tasks;
import com.github.kagkarlsson.scheduler.task.schedule.Schedules;
import cz.kpsys.portaro.commons.concurrent.ReentrantLockLocker;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.object.repo.TransactionalSaver;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.licence.*;
import cz.kpsys.portaro.licence.portaro.VcsPortaroVersion;
import cz.kpsys.portaro.platform.PlatformUtils;
import cz.kpsys.portaro.setting.*;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.view.web.LicenceApiController;
import cz.kpsys.portaro.view.web.LicenceKeyApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestOperations;

import java.time.Duration;
import java.util.function.Predicate;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LicenceUpdaterConfig {

    @NonNull RestOperations rest;
    @NonNull SettingLoader settingLoader;
    @NonNull Saver<CustomSetting<String>, CustomSettingEntity> customSettingSaver;
    @NonNull Provider<@NonNull String> rootSerialCodeProvider;
    @NonNull Provider<@NullableNotBlank String> rootServerUrlProvider;
    @NonNull VcsPortaroVersion portaroVersion;
    @NonNull Provider<@NonNull BusinessVersion> currentVersionProvider;
    @NonNull Provider<@NonNull BusinessVersion> licenceVersionProvider;
    @NonNull Provider<@NonNull Integer> rootDepartmentIdProvider;
    @NonNull LicenceKeyDecoder licenceKeyDecoder;
    @NonNull CustomSettingLoader customSettingLoader;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull HealthIndicator databaseStructureHealthIndicator;

    @Bean
    public LicenceKeyApiController licenceKeyApiController() {
        return new LicenceKeyApiController(settingLoader.getDepartmentedProvider(SettingKeys.LICENCE_KEY));
    }

    @Bean
    public LicenceApiController licenceApiController() {
        return new LicenceApiController(
                licenceProvider(),
                savingLicenceServerJwtLicenceKeyProvider()
        );
    }

    @Bean
    public Provider<@NonNull Licence> licenceProvider() {
        return new LicenceKeyDecodingLicenceProvider(
                jwtLicenceKeyProvider(),
                licenceKeyDecoder
        );
    }

    @Bean
    public Provider<@NonNull String> jwtLicenceKeyProvider() {
        Provider<@NonNull String> fromIniProvider = settingLoader.getOnRootProvider(SettingKeys.LICENCE_KEY).throwingWhenNull();
        Predicate<@NonNull String> keyExpired = new LicenceKeyDecodingLicenceKeyExpiredPredicate(licenceKeyDecoder);
        ValidatingProvider<@NonNull String> validatingFromIniProvider = ValidatingProvider.invalidWhen(fromIniProvider, keyExpired, expiredJwt -> new LicenceKeyExpiredException());

        return new FallbackedProvider<>(validatingFromIniProvider, savingLicenceServerJwtLicenceKeyProvider())
                .fallbackOnException(IniNotSetException.class)
                .fallbackOnException(LicenceKeyExpiredException.class);
    }

    @Bean
    public Provider<@NonNull String> savingLicenceServerJwtLicenceKeyProvider() {
        LicenceServerFetchingLicenceKeyProvider fromLicenceServerProvider = new LicenceServerFetchingLicenceKeyProvider(
                rest,
                rootSerialCodeProvider,
                settingLoader.getOnRootProvider(SettingKeys.KPSYS_API_KEY).throwingWhenNull())
                .withExtraData("seatable.provided_url", rootServerUrlProvider)
                .withExtraData("seatable.current_licence_version", () -> licenceVersionProvider.get().getValue())
                .withExtraData("seatable.db_structure_health", () -> databaseStructureHealthIndicator.getHealth(false).getStatus().getCode())
                .withExtraData("seatable.portaro_version", portaroVersion::getValue)
                .withExtraData("seatable.portaro_branch", portaroVersion::getBranch)
                .withExtraData("seatable.portaro_business_version", () -> currentVersionProvider.get().getValue())
                .withExtraData("seatable.portaro_java_version", () -> SystemUtils.JAVA_VERSION)
                .withExtraData("seatable.portaro_os_type", () -> switch (PlatformUtils.getOperatingSystemType()) {
                    case WINDOWS -> "windows";
                    case LINUX -> "linux";
                    case MAC_OS -> "macos";
                    case OTHER -> "other";
                })
                .withExtraData("seatable.portaro_os_version", () -> SystemUtils.OS_VERSION);
        SavingProvider savingProvider = new SavingProvider(
                fromLicenceServerProvider,
                licenceKeySaver()
        );
        return new LockingProvider<>(savingProvider, new ReentrantLockLocker());
    }

    @Bean
    public Saver<String, String> licenceKeySaver() {
        return new TransactionalSaver<>(
                new DelegatingValueCustomSettingSaver(customSettingSaver, SettingKeys.LICENCE_KEY, rootDepartmentIdProvider, customSettingLoader),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public Task<Void> licenceUpdateTask() {
        return Tasks.recurring("licence-update-task", Schedules.fixedDelay(Duration.ofHours(9)))
                .execute((_, _) -> savingLicenceServerJwtLicenceKeyProvider().get());
    }
}
