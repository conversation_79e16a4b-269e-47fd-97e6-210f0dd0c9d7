package cz.kpsys.portaro.commons.object.repo;

import lombok.NonNull;

import java.util.function.Consumer;
import java.util.function.Function;

public interface HookableSaver<E, RET> extends Saver<E, RET> {

    HookableSaver<E, RET> addPreHook(@NonNull Function<E, E> preHook);

    default HookableSaver<E, RET> addPreHook(@NonNull Runnable postSuccessHook) {
        return addPreHook(unused -> {
            postSuccessHook.run();
            return unused;
        });
    }

    HookableSaver<E, RET> addPostSuccessHook(@NonNull Consumer<RET> postSuccessHook);

    default HookableSaver<E, RET> addPostSuccessHook(@NonNull Runnable postSuccessHook) {
        return addPostSuccessHook(unused -> postSuccessHook.run());
    }

}
