package cz.kpsys.portaro.oai.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import jakarta.annotation.Nullable;
import lombok.NonNull;

public record RecordResponse(

        @JacksonXmlProperty(localName = "header", namespace = "http://www.openarchives.org/OAI/2.0/")
        @NonNull
        HeaderResponse header,

        /**
         * Null, when status (in header) is "deleted"
         */
        @JacksonXmlProperty(localName = "metadata", namespace = "http://www.openarchives.org/OAI/2.0/")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @Nullable
        MetadataContainer metadata

) {

    public static RecordResponse ofActive(@NonNull HeaderResponse header, @NonNull MetadataContainer metadata) {
        return new RecordResponse(header, metadata);
    }

    public static RecordResponse ofDeleted(@NonNull HeaderResponse header) {
        return new RecordResponse(header, null);
    }

}
