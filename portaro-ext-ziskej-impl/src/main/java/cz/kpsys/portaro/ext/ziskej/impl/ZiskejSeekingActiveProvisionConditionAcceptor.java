package cz.kpsys.portaro.ext.ziskej.impl;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.ziskej.ContextualZiskejClient;
import cz.kpsys.portaro.ext.ziskej.json.ZkSubticketTransitionIdZiskejRequest;
import cz.kpsys.portaro.ext.ziskej.model.ZiskejZkSubticketTransitionPerformCommand;
import cz.kpsys.portaro.loan.ill.process.SeekingActiveProvisionConditionAcceptCommand;
import cz.kpsys.portaro.loan.ill.process.SeekingActiveProvisionConditionAcceptor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ZiskejSeekingActiveProvisionConditionAcceptor implements Consumer<@NonNull SeekingActiveProvisionConditionAcceptCommand> {

    @NonNull SeekingActiveProvisionConditionAcceptor standardAcceptor;
    @NonNull ContextualZiskejClient<Department> contextualZiskejClient;

    @Transactional
    @Override
    public void accept(@NonNull SeekingActiveProvisionConditionAcceptCommand command) {
        standardAcceptor.accept(command);

        String ziskejSubticketId = command.seeking().getActiveProvision().ziskejSubticketSyncId();
        performZiskejAssignment(ziskejSubticketId, command.ctx());
    }

    private void performZiskejAssignment(@NonNull String ziskejSubticketId, @NonNull Department ctx) {
        ZiskejZkSubticketTransitionPerformCommand transitionPerformCommand = new ZiskejZkSubticketTransitionPerformCommand(
                ziskejSubticketId,
                ZkSubticketTransitionIdZiskejRequest.ACCEPT,
                null
        );
        contextualZiskejClient.performZkSubticketTransition(ctx, transitionPerformCommand);
    }
}
