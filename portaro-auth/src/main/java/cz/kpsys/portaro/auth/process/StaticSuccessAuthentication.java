package cz.kpsys.portaro.auth.process;

import cz.kpsys.portaro.auth.Authenticity;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.AuthableUser;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.HashSet;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class StaticSuccessAuthentication extends GenericAuthoritiedSuccessAuthentication {

    @Getter @NonNull Authenticity authenticity;

    private StaticSuccessAuthentication(AuthableUser user, @NonNull Authenticity authenticity, @NonNull Department ctx) {
        super(user, ctx);
        this.authenticity = authenticity;
    }

    private StaticSuccessAuthentication(AuthableUser user, Collection<? extends GrantedAuthority> authorities, @NonNull Authenticity authenticity, @NonNull Department ctx) {
        super(user, ctx, new HashSet<>(authorities));
        this.authenticity = authenticity;
    }

    public static StaticSuccessAuthentication authenticatedForTesting(AuthableUser user, @NonNull Department ctx) {
        return new StaticSuccessAuthentication(user, Authenticity.ABSOLUTE, ctx);
    }

    public static StaticSuccessAuthentication authenticatedForBackgroundJobs(AuthableUser user, @NonNull Department ctx) {
        return new StaticSuccessAuthentication(user, Authenticity.ABSOLUTE, ctx);
    }

    public static StaticSuccessAuthentication authenticatedAsForcedAnonymous(@NonNull AuthableUser anonymousUser, @NonNull Department ctx) {
        return new StaticSuccessAuthentication(anonymousUser, Authenticity.ABSOLUTE, ctx);
    }

    public static StaticSuccessAuthentication authenticatedAsForced(AuthableUser user, @NonNull Department ctx) {
        return new StaticSuccessAuthentication(user, Authenticity.ABSOLUTE, ctx);
    }

    public static StaticSuccessAuthentication authenticatedAsExternal(AuthableUser user, @NonNull Department ctx) {
        return new StaticSuccessAuthentication(user, Authenticity.ONE_CONFIDENTAL_FACTOR_OR_BETTER, ctx);
    }

    public static StaticSuccessAuthentication authenticated(AuthableUser user, Collection<? extends GrantedAuthority> authorities, Authenticity authenticity, @NonNull Department ctx) {
        return new StaticSuccessAuthentication(user, authorities, authenticity, ctx);
    }

}
