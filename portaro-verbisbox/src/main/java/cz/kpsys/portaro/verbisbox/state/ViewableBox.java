package cz.kpsys.portaro.verbisbox.state;

import cz.kpsys.portaro.verbisbox.shipment.ViewableItemInsight;
import cz.kpsys.portaro.verbisbox.shipment.ViewableUser;
import cz.kpsys.portaro.verbisboxer.manager.state.BoxDoorStateResponse;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public record ViewableBox(

        @NonNull
        UUID id,

        @Nullable
        Instant insertDate,

        @Nullable
        Instant assignDate,

        @Nullable
        Instant pickupDeadlineDate,

        @NonNull
        List<@NonNull ViewableItemInsight> items,

        @NonNull
        BoxDoorStateResponse doorState,

        @Nullable
        ViewableUser user
) {
}
