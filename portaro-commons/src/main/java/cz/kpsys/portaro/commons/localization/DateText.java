package cz.kpsys.portaro.commons.localization;

import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.time.ZoneId;
import java.util.Locale;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
public class DateText implements Text {

    @NonNull Instant date;
    @NonNull LocaledDateFormatterResolver localedDateFormatterResolver;

    public static DateText createWithoutTime(@NonNull Instant date) {
        return new DateText(date, LocaledDateFormatterResolver.createWithoutTime());
    }


    @Override
    public String toString() {
        return "DateText %s".formatted(date);
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public <CTX> Optional<String> tryLocalize(Translator<CTX> translator, CTX ctx, Locale locale) {
        String formattedDate = localedDateFormatterResolver.getFormatter(locale).format(date.atZone(ZoneId.systemDefault()));
        return Optional.of(formattedDate);
    }

}
