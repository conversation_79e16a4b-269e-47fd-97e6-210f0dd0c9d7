package cz.kpsys.portaro.view.web.rest.user;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.FileAccessType;
import cz.kpsys.portaro.file.filecategory.FileCategory;
import cz.kpsys.portaro.record.binding.user.UploadedUserFilesCommand;
import cz.kpsys.portaro.user.User;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public record UploadedUserFilesRequest(

        @NotNull
        User user,

        @Schema(description = "Files encoded as multipart data")
        @NotEmpty
        List<MultipartFile> files,

        @Nullable
        FileAccessType accessType,

        @Nullable
        FileCategory category

) {

    public UploadedUserFilesCommand toCommand(@NonNull Department ctx,
                                              @NonNull UserAuthentication currentAuth) {
        return new UploadedUserFilesCommand(ctx, currentAuth, user(), files(), accessType(), category());
    }

}