package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class LoanRequestModal {

    final PageUtils pageUtils;

    @FindBy(css = "button[data-qa='login-first-button']")
    WebElement loginFirstButton;

    @FindBy(css = "button[data-qa='standard-order-button']")
    WebElement orderDocumentButton;

    @FindBy(css = "button[data-qa='standard-reservation-button']")
    WebElement reservationDocumentButton;

    public LoanRequestModal(@NonNull WebDriver driver) {
        pageUtils = new PageUtils(driver);
        PageFactory.initElements(driver, this);
    }

    public LoanRequestModal clickLoginFirstButton() {
        pageUtils.waitForSteadinessAndClick(loginFirstButton);
        return this;
    }

    public LoanRequestModal clickOnOrderDocumentButton() {
        pageUtils.waitForSteadinessAndClick(orderDocumentButton);
        return this;
    }

    public LoanRequestModal clickOnReservationDocumentButton() {
        pageUtils.waitForSteadinessAndClick(reservationDocumentButton);
        return this;
    }


}
