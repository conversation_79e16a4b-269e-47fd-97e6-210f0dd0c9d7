package cz.kpsys.portaro.record.export.marc;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.export.RecordExport;
import lombok.NonNull;

import java.util.List;

public interface MarcRecordsEnhancer {

    List<MappedRecord> enhance(@NonNull List<MappedRecord> mappedRecords,
                               @NonNull RecordExport recordExport,
                               @NonNull UserAuthentication currentAuth,
                               @NonNull Department ctx);

    default ConditionalMarcRecordsEnhancer enabledOnlyIf(@NonNull ContextualProvider<Department, @NonNull Boolean> enabledProvider) {
        return new ConditionalMarcRecordsEnhancer(this, enabledProvider);
    }

}
