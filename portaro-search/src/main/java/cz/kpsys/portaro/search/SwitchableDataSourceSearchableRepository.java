package cz.kpsys.portaro.search;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.object.repo.SwitchableDataSource;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.List;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SwitchableDataSourceSearchableRepository<P extends SearchParams, ITEM extends Identified<ID>, ID> implements SearchableRepository<P, ITEM, ID>, SwitchableDataSource {

    @NonNull SearchableRepository<P, ITEM, ID> persistentDataSourceRepository;
    @NonNull Provider<Boolean> switchConditionProvider;
    @NonNull @NonFinal SearchableRepository<P, ITEM, ID> selectedRepository;

    public SwitchableDataSourceSearchableRepository(@NonNull SearchableRepository<P, ITEM, ID> temporaryDataSourceRepository,
                                                    @NonNull SearchableRepository<P, ITEM, ID> persistentDataSourceRepository,
                                                    @NonNull Provider<Boolean> switchConditionProvider) {
        this.persistentDataSourceRepository = persistentDataSourceRepository;
        this.switchConditionProvider = switchConditionProvider;
        this.selectedRepository = temporaryDataSourceRepository;
    }

    @Override
    public boolean canBeSwitched() {
        return switchConditionProvider.get();
    }

    @Override
    public void switchToPermanentDataSource() {
        if (!canBeSwitched()) {
            return;
        }
        selectedRepository = persistentDataSourceRepository;
    }

    @Override
    public void delete(@NonNull ITEM item) throws DataAccessException {
        selectedRepository.delete(item);
    }

    @Override
    public ITEM getById(@NonNull ID id) throws ItemNotFoundException {
        return selectedRepository.getById(id);
    }

    @Override
    public @NonNull ITEM save(@NonNull ITEM item) {
        return selectedRepository.save(item);
    }

    @Override
    public Chunk<ITEM, RangePaging> getPage(RangePaging paging, SortingItem customSorting, P params) {
        return selectedRepository.getPage(paging, customSorting, params);
    }

    @Override
    public int getTotalElements(P params) {
        return selectedRepository.getTotalElements(params);
    }

    @Override
    public List<ITEM> getContent(RangePaging paging, SortingItem customSorting, Consumer<P> paramsModifier) {
        return selectedRepository.getContent(paging, customSorting, paramsModifier);
    }

    @Override
    public int getTotalElements(Consumer<P> paramsModifier) {
        return selectedRepository.getTotalElements(paramsModifier);
    }

    @Override
    public List<ITEM> getAllByIds(@NonNull List<ID> ids) {
        return selectedRepository.getAllByIds(ids);
    }
}
