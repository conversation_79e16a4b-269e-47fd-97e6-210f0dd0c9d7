import type {SvelteContext} from 'core/types';
import {registerCustomElement} from './register-custom-element';
import {camelCaseToKebabCase} from 'shared/utils/string-utils';
import {CUSTOM_ELEMENTS_REGISTRY} from './custom-elements.registry';

/*@ngInject*/
export function customElementsHtmlSanitizationConfig($sanitizeProvider) {
    CUSTOM_ELEMENTS_REGISTRY.forEach(({name, propsNames = []}) => {
        $sanitizeProvider.addValidElements([name]);
        $sanitizeProvider.addValidAttrs(propsNames.map(camelCaseToKebabCase));
    });
}

/*@ngInject*/
export function customElementsConfig(svelteDefaultContext: SvelteContext) {
    CUSTOM_ELEMENTS_REGISTRY.forEach(({name, alternativeNames, componentConstructor, propsNames = []}) => {
        registerCustomElement(name, alternativeNames, componentConstructor, svelteDefaultContext, propsNames);
    });
}