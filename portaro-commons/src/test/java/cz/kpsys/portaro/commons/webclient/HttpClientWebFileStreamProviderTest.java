package cz.kpsys.portaro.commons.webclient;

import cz.kpsys.portaro.commons.io.BytesRange;
import cz.kpsys.portaro.commons.io.ToByteArraySavingFileStreamConsumer;
import lombok.NonNull;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@Tag("not-ci")
@Tag("unit")
class HttpClientWebFileStreamProviderTest {

    @Test
    public void shouldLoad() {
        RestRemoteFileDataStreamer externalFileDataStreamer = new RestRemoteFileDataStreamer(createRestTemplate());
        HttpClientWebFileDataStreamer streamProvider = new HttpClientWebFileDataStreamer(externalFileDataStreamer);
        ToByteArraySavingFileStreamConsumer reader = new ToByteArraySavingFileStreamConsumer();
        streamProvider.streamData(123L, "http://speedtest.ftp.otenet.gr/files/test10Mb.db", reader, null, null);

        assertTrue(reader.getSize() > 0);
        assertTrue(reader.getData().length > 0);
        assertEquals(reader.getSize(), reader.getData().length);
    }

    @Test
    public void shouldLoadRanged() {
        RestRemoteFileDataStreamer externalFileDataStreamer = new RestRemoteFileDataStreamer(createRestTemplate());
        HttpClientWebFileDataStreamer streamProvider = new HttpClientWebFileDataStreamer(externalFileDataStreamer);
        ToByteArraySavingFileStreamConsumer reader = new ToByteArraySavingFileStreamConsumer();
        streamProvider.streamData(123L, "http://speedtest.ftp.otenet.gr/files/test10Mb.db", reader, null, new BytesRange(Optional.of((long) 0), Optional.of((long) 99)));

        assertTrue(reader.getSize() > 0);
        assertEquals(100, reader.getData().length);
        assertEquals(reader.getSize(), reader.getData().length);
    }

    @Test
    public void shouldSupportError404() {
        RestRemoteFileDataStreamer externalFileDataStreamer = new RestRemoteFileDataStreamer(createRestTemplate());
        HttpClientWebFileDataStreamer streamProvider = new HttpClientWebFileDataStreamer(externalFileDataStreamer);
        ToByteArraySavingFileStreamConsumer reader = new ToByteArraySavingFileStreamConsumer();
        assertThrows(HttpClientErrorException.NotFound.class, () -> streamProvider.streamData(123L, "http://speedtest.ftp.otenet.gr/files/not-existing-file.db", reader, null, null));
    }

    @NonNull
    private static RestTemplate createRestTemplate() {
        return new RestTemplate(new HttpComponentsClientHttpRequestFactory(new HttpFactory("portaro/test").httpClientBuilder().build()));
    }

}