package cz.kpsys.portaro.commons.containers.server;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.mockserver.matchers.Times;
import org.testcontainers.containers.Network;


import static cz.kpsys.portaro.commons.web.HttpHeaderConstants.ContentType.Value.XML;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class UnisMockServer extends MockServer {

    public UnisMockServer(@NonNull Network network) {
        super("unismockserver", network);
    }

    public void registerUnisResponse(@NonNull String testNetId, @NonNull String firstName, @NonNull String lastName) {
        String unisApiPath = "/PersonWS";
        registerResponseToRequest(unisApiPath, getSingleUserUnisData(testNetId, firstName, lastName), XML, Times.once());
        registerResponseToRequest(unisApiPath, getFindUpdatedPeopleXmlResponse(testNetId), XML, Times.once());
        registerResponseToRequest(unisApiPath, getStudentXmlResponse(testNetId, firstName, lastName), XML, Times.once());
        registerResponseToRequest(unisApiPath, getFindUpdatedPeopleXmlResponse(testNetId), XML, Times.once());
        registerResponseToRequest(unisApiPath, getStudentEndRelationResponse(testNetId, firstName, lastName), XML, Times.once());
    }

    private String getFindUpdatedPeopleXmlResponse(@NonNull String testNetId) {
        return """
            <?xml version="1.0" encoding="UTF-8"?>
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                <soapenv:Body>
                    <a:findUpdatedPeopleResponse xmlns:a="http://edison.vsb.cz/PersonWS/">
                            <login>%s</login>
                        </a:findUpdatedPeopleResponse>
                    </soapenv:Body>
                </soapenv:Envelope>
            """.formatted(testNetId);
    }

    private String getStudentXmlResponse(@NonNull String testNetId, @NonNull String firstName, @NonNull String lastName) {
        return """
            <?xml version="1.0" encoding="UTF-8"?>
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                <soapenv:Body>
                    <a:findReaderInfoResponse xmlns:a="http://edison.vsb.cz/PersonWS/">
                        <person>
                            <login>%s</login>
                            <firstName>%s</firstName>
                            <lastName>%s</lastName>
                            <birthDate>1999-01-05</birthDate>
                            <citizenshipCode>203</citizenshipCode>
                            <email><EMAIL></email>
                            <privatePhone>730993955</privatePhone>
                            <privateEmail><EMAIL></privateEmail>
                            <chipNum1>042A5012FB6380</chipNum1>
                            <residencyAddress>
                                <street>Budovatelů</street>
                                <number>1778/13</number>
                                <city>Nový Jičín</city>
                                <zipCode>74101</zipCode>
                                <country>Česká republika</country>
                                <countryCode>203</countryCode>
                            </residencyAddress>
                            <mailingAddress>
                                <street>Budovatelů</street>
                                <number>1778/13</number>
                                <city>Nový Jičín</city>
                                <zipCode>74101</zipCode>
                                <country>Česká republika</country>
                                <countryCode>203</countryCode>
                            </mailingAddress>
                            <relations>
                                <relation>
                                    <relationId>458110</relationId>
                                    <relationTypeId>3</relationTypeId>
                                    <beginDate>2018-09-03</beginDate>
                                    <endDate>9999-12-31</endDate>
                                    <orgUnitAbbrev>FMT</orgUnitAbbrev>
                                    <orgUnitTitle>Fakulta materiálově-technologická</orgUnitTitle>
                                    <workplaceAbbrev>FMT</workplaceAbbrev>
                                    <studyTypeId>1</studyTypeId>
                                    <studyTypeCode>B</studyTypeCode>
                                    <studyFormId>1</studyFormId>
                                    <studyFormCode>P</studyFormCode>
                                    <studyProgrammeCode>B3909</studyProgrammeCode>
                                    <studyProgrammeTitle>Procesní inženýrství</studyProgrammeTitle>
                                    <studyBranchCode>3909R014</studyBranchCode>
                                    <studyBranchTitle>Procesní inženýrství a metody kontroly kvality</studyBranchTitle>
                                    <studyResidency>false</studyResidency>
                                    <studyActive>true</studyActive>
                                </relation>
                            </relations>
                        </person>
                    </a:findReaderInfoResponse>
                </soapenv:Body>
            </soapenv:Envelope>
            """.formatted(testNetId, firstName, lastName);
    }

    private String getStudentEndRelationResponse(@NonNull String testNetId, @NonNull String firstName, @NonNull String lastName) {
        return """
            <?xml version="1.0" encoding="UTF-8"?>
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                <soapenv:Body>
                    <a:findReaderInfoResponse xmlns:a="http://edison.vsb.cz/PersonWS/">
                        <person>
                            <login>%s</login>
                            <firstName>%s</firstName>
                            <lastName>%s</lastName>
                            <birthDate>1992-08-06</birthDate>
                            <chipNum1>044F2DB2BC6380</chipNum1>
                            <relations>
                                <relation>
                                    <endDate>2021-07-16</endDate>
                                </relation>
                            </relations>
                        </person>
                    </a:findReaderInfoResponse>
                </soapenv:Body>
            </soapenv:Envelope>
            """.formatted(testNetId, firstName, lastName);
    }

    private String getSingleUserUnisData(@NonNull String testNetId, @NonNull String firstName, @NonNull String lastName) {
        return """
            <?xml version="1.0" encoding="UTF-8"?>
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                    <soapenv:Body>
                        <a:findReaderInfoResponse xmlns:a="http://edison.vsb.cz/PersonWS/">
                            <person>
                                <login>%s</login>
                                <firstName>%s</firstName>
                                <lastName>%s</lastName>
                                <degreeBefore>Ing.</degreeBefore>
                                <birthDate>1993-10-04</birthDate>
                                <citizenshipCode>703</citizenshipCode>
                                <email><EMAIL></email>
                                <phone>+420 596 995 466</phone>
                                <privatePhone>421917043124</privatePhone>
                                <privateEmail><EMAIL></privateEmail>
                                <chipNum1>04437C5AFE5E80</chipNum1>
                                <residencyAddress>
                                    <street>Kostolné</street>
                                    <number>67</number>
                                    <city>Kostolné</city>
                                    <zipCode>91613</zipCode>
                                    <country>Slovenská republika</country>
                                    <countryCode>703</countryCode>
                                </residencyAddress>
                                <mailingAddress>
                                    <street>Matěje Kopeckého 606</street>
                                    <number>22</number>
                                    <city>Ostrava</city>
                                    <zipCode>70800</zipCode>
                                    <country>Česká republika</country>
                                    <countryCode>203</countryCode>
                                </mailingAddress>
                                <relations>
                                    <relation>
                                        <relationId>479879</relationId>
                                        <relationTypeId>3</relationTypeId>
                                        <beginDate>2019-09-16</beginDate>
                                        <endDate>9999-12-31</endDate>
                                        <orgUnitAbbrev>617</orgUnitAbbrev>
                                        <orgUnitTitle>Katedra chemie</orgUnitTitle>
                                        <workplaceAbbrev>FMT</workplaceAbbrev>
                                        <studyTypeId>2</studyTypeId>
                                        <studyTypeCode>D</studyTypeCode>
                                        <studyFormId>1</studyFormId>
                                        <studyFormCode>P</studyFormCode>
                                        <studyProgrammeCode>P3909</studyProgrammeCode>
                                        <studyProgrammeTitle>Procesní inženýrství</studyProgrammeTitle>
                                        <studyBranchCode>3909V003</studyBranchCode>
                                        <studyBranchTitle>Procesní inženýrství</studyBranchTitle>
                                        <studyResidency>false</studyResidency>
                                        <studyActive>true</studyActive>
                                    </relation>
                                </relations>
                            </person>
                        </a:findReaderInfoResponse>
                    </soapenv:Body>
                </soapenv:Envelope>
            """.formatted(testNetId, firstName, lastName);
    }
}
