package cz.kpsys.portaro.commons.object;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Provider for simplification of generating e.g. urls with context path provider-
 * Created by <PERSON> on 14.05.2017.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ChainingStringProvider implements Provider<@NonNull String> {

    @NonNull List<Provider<@NonNull String>> providers;
    @NonFinal String delimiter = "";

    public static ChainingStringProvider ofFixedPrefix(@NonNull String prefix, Provider<@NonNull String> suffixProvider) {
        return new ChainingStringProvider(List.of(StaticProvider.of(prefix), suffixProvider));
    }

    public static ChainingStringProvider ofFixedSuffix(Provider<@NonNull String> prefixProvider, @NonNull String suffix) {
        return new ChainingStringProvider(List.of(prefixProvider, StaticProvider.of(suffix)));
    }

    public static ChainingStringProvider ofProvidedInfix(String prefix, Provider<String> infixProvider, String suffix) {
        return new ChainingStringProvider(List.of(StaticProvider.of(prefix), infixProvider, StaticProvider.of(suffix)));
    }

    @SafeVarargs
    public static ChainingStringProvider of(Provider<String>... providers) {
        return new ChainingStringProvider(List.of(providers));
    }

    public ChainingStringProvider delimitedBy(String delimiter) {
        this.delimiter = delimiter;
        return this;
    }

    @Override
    public String get() {
        return providers.stream()
                .map(Provider::get)
                .collect(Collectors.joining(delimiter));
    }
}
