<script lang="ts">
    import type {ValueEditorSize} from '../../../types';
    import type {FormControlOptions} from '../../forms/types';
    import type {ValueEditorValidations} from '../../../kp-value-editor/types';
    import type {SearchOrEditValueEditorLocalizations, SearchOrEditValueEditorOptions} from './types';
    import type {FormControl} from '../../forms/form-control';
    import {FormControlBuilder} from '../../forms/form-control';
    import {formControls} from '../../forms/use.form-controls';
    import ErrorMessages from '../../errors/ErrorMessages.svelte';
    import type {EditorsLocalizationFunction} from '../../../localizations/types';
    import {touched} from '../../shared/use.touched';
    import {getContext, onDestroy, onMount} from 'svelte';
    import {focusOnMount} from '../../shared/use.focus-on-mount';
    import {EDITORS_LOCALIZE} from '../../../context-keys';
    import KpLoadingInline from '../../../../components/kp-loading/KpLoadingInline.svelte';
    import {isNullOrUndefined} from 'shared/utils/custom-utils';
    import {emptyAsNullFormatter, emptyAsNullParser} from '../_shared/editors-utils';
    import {ariaInvalid} from '../../errors/use.aria-invalid';
    import {ariaErrormessage} from '../../errors/use.aria-errormessage';
    import {validatorFactory} from './utils';
    import {createInputDescription} from '../../description/utils';
    import {ariaDescribedby} from '../../description/use.aria-describedby';
    import Description from '../../description/Description.svelte';
    import {required} from '../../forms/use.required';
    import {getLogger} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let model: unknown;
    export let formControlOptions: FormControlOptions;
    export let editorId: string;
    export let editorName: string;
    export let placeholder: string;
    export let isDisabled: boolean;
    export let isFocused: boolean;
    export let forceShowErrors: boolean;
    export let size: ValueEditorSize;

    export let options: SearchOrEditValueEditorOptions<unknown>;
    export let validations: ValueEditorValidations;

    const localize: EditorsLocalizationFunction<SearchOrEditValueEditorLocalizations> = getContext(EDITORS_LOCALIZE);
    let searching = false;
    let editing = false;

    const formControl = FormControlBuilder.for(editorId, editorName, model)
        .withValidators(validatorFactory(validations))
        .withModifiers([emptyAsNullParser(options)])
        .withFormatters([emptyAsNullFormatter(options)])
        .withOptions(formControlOptions)
        .build(getLogger());

    const modelSubscription = formControl.getModelValue$().subscribe((value) => model = value);  // two-way data binding -> sending updated value up to a parent
    $: formControl.setModelValue(model); // two-way data binding -> receiving new model value from parent

    export function getFormController(): FormControl<unknown> {
        return formControl;
    }

    const inputDescription = createInputDescription({editorId, editorName, description: localize('description')});

    onMount(() => {
        if (options.immediatelyTriggerSearch) {
            search();
        }
    });

    onDestroy(() => {
        modelSubscription.unsubscribe();
    })

    function setToEmpty() {
        formControl.setViewValue(null);
    }

    async function edit() {
        editing = true;
        try {
            const editedModel = await options.editModelFunction({
                model,
                editParams: options.editParams
            });
            formControl.setViewValue(editedModel);
        } finally {
            editing = false;
        }
    }

    async function search() {
        searching = true;
        try {
            const searchedModel = await options.searchModelFunction({
                model,
                searchParams: options.searchParams,
                createParams: options.createParams
            });
            formControl.setViewValue(searchedModel);
        } finally {
            searching = false;
        }
    }

    function hasEditModelFunction(): boolean {
        return !isNullOrUndefined(options.editModelFunction);
    }

    function hasSearchModelFunction(): boolean {
        return !isNullOrUndefined(options.searchModelFunction);
    }
</script>

<div class="search-or-edit-value-editor">
    {#if model}
        <span class="model-value size-{size}">
            <svelte:component value={model} this={options.modelComponent}/>
        </span>
    {:else}
        <span class="text-muted placeholder size-{size}">{placeholder}</span>
    {/if}

    {#if hasSearchModelFunction()}
        <button class="search-button btn btn-default btn-{size}"
                type="button"
                disabled={isDisabled || searching}
                use:touched={{formControl}}
                use:focusOnMount={isFocused}
                on:click={search}>

            {#if searching}
                <KpLoadingInline size="xs"/>
            {:else}
                <UIcon icon="search"/>
            {/if}

            {#if model}
                <span>{localize('searchOther')}</span>
            {:else}
                <span>{localize('search')}</span>
            {/if}
        </button>
    {/if}

    {#if hasEditModelFunction()}
        <button class="edit-button btn btn-default btn-{size}"
                type="button"
                disabled={isDisabled || editing}
                use:touched={{formControl}}
                on:click={edit}>

            {#if editing}
                <KpLoadingInline size="xs"/>
            {:else}
                <UIcon icon="edit"/>
            {/if}

            {#if model}
                <span>{localize('editValue')}</span>
            {:else}
                <span>{localize('createNew')}</span>
            {/if}
        </button>
    {/if}

    {#if options.allowToDeleteValue}
        <button class="delete-button btn btn-default btn-{size}"
                type="button"
                disabled={isDisabled || model === null}
                use:touched={{formControl}}
                on:click={setToEmpty}>
            <UIcon icon="trash"/>
            <span>{localize('delete')}</span>
        </button>
    {/if}

    <input class="validation-helper"
           type="hidden"
           id={editorId}
           name={editorName}
           use:required={validations.required}
           use:ariaErrormessage={formControl}
           use:ariaInvalid={formControl}
           use:formControls={formControl}
           use:ariaDescribedby={inputDescription}
           disabled={isDisabled}
           data-main-input/>

    <ErrorMessages customClass="searchable" standalone inline formController={formControl} {forceShowErrors} {size}/>
    <Description {inputDescription}/>
</div>

<style lang="less">
    :global(kp-value-editor[data-type="search-or-edit"] .error-message.searchable) {
        margin-left: 5px;
    }

    button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .placeholder:empty {
        display: none;
    }

    .placeholder.size-xs,
    .placeholder.size-sm {
        font-size: 12px;
    }

    .search-or-edit-value-editor {
        display: flex;
        gap: 5px;
        align-items: center;
    }
</style>