package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.cache.StaticCache;
import cz.kpsys.portaro.commons.object.Identified;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;

/**
 * Cached codebook backed by allValuesProvider and Static cache
 * @param <E>
 * @param <ID>
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CachedCodebook<E extends Identified<ID>, ID> implements Codebook<E, ID>, CacheCleaner {

    @NonNull StaticCache<E> cache;

    @Override
    public E getById(@NonNull ID id) {
        return findById(id)
                .orElseThrow(() -> ItemNotFoundException.ofResolvedDesiredItemType(getAll(), id));
    }

    @Override
    public Optional<E> findById(@NonNull ID id) {
        return cache.findById(id);
    }

    @Override
    public List<E> getAll() {
        return cache.getAll();
    }

    @Override
    public void clearCache() {
        cache.clear();
    }

    @Override
    public String toString() {
        return "CachedCodebook{cache=%s}".formatted(cache);
    }
}
