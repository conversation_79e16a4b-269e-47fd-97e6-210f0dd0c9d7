package cz.kpsys.portaro.security.export;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.export.Exporter;
import cz.kpsys.portaro.security.Action;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.TypeDescriptor;

import java.util.Locale;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredExporter<E> implements Exporter<E> {

    @NonNull Exporter<E> delegate;
    @NonNull SecurityManager securityManager;
    @NonNull Action<E> actionToCheck;

    @Override
    public void export(@NonNull E object, @NonNull UserAuthentication currentAuth, Department ctx, Locale locale, FileStreamConsumer streamConsumer) {
        securityManager.throwIfCannot(actionToCheck, currentAuth, ctx, object);
        delegate.export(object, currentAuth, ctx, locale, streamConsumer);
    }

    @Override
    public TypeDescriptor getType() {
        return delegate.getType();
    }
}
