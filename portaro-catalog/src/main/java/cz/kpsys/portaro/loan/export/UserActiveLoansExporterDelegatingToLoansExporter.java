package cz.kpsys.portaro.loan.export;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.export.Exporter;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoansProviderService;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.TypeDescriptor;

import java.util.List;
import java.util.Locale;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserActiveLoansExporterDelegatingToLoansExporter implements Exporter<BasicUser> {

    @NonNull Exporter<List<Loan>> delegate;
    @NonNull LoansProviderService loansProviderService;
    @NonNull UserByBasicUserLoader userLoader;

    @Override
    public void export(@NonNull BasicUser object, @NonNull UserAuthentication currentAuth, Department ctx, Locale locale, FileStreamConsumer streamConsumer) {
        User user = userLoader.getUser(object);
        List<Loan> loans = loansProviderService.getActiveLoans(user, Range.forAll(), ctx, currentAuth);
        delegate.export(loans, currentAuth, ctx, locale, streamConsumer);
    }

    @Override
    public TypeDescriptor getType() {
        return TypeDescriptor.valueOf(BasicUser.class);
    }

}
