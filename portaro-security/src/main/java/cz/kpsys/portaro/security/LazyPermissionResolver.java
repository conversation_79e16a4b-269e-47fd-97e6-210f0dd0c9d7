package cz.kpsys.portaro.security;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
class LazyPermissionResolver<SUBJECT> implements PermissionResolver<SUBJECT> {

    @NonNull PermissionRegistry permissionRegistry;
    @NonNull Action<SUBJECT> action;

    @Override
    public PermissionResult can(UserAuthentication auth, Department ctx, SUBJECT subject) {
        return permissionRegistry.get(action).can(auth, ctx, subject);
    }

    @Override
    public String toString() {
        return "LazyPermissionResolver[%s]".formatted(action);
    }
}
