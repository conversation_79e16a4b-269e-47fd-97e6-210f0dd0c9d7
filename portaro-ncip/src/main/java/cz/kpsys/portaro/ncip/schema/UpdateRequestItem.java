
package cz.kpsys.portaro.ncip.schema;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}InitiationHeader" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}MandatedAction" minOccurs="0"/>
 *         &lt;choice>
 *           &lt;sequence>
 *             &lt;choice>
 *               &lt;element ref="{http://www.niso.org/2008/ncip}UserId"/>
 *               &lt;element ref="{http://www.niso.org/2008/ncip}AuthenticationInput" maxOccurs="unbounded"/>
 *             &lt;/choice>
 *             &lt;element ref="{http://www.niso.org/2008/ncip}ItemId"/>
 *             &lt;element ref="{http://www.niso.org/2008/ncip}RequestType"/>
 *           &lt;/sequence>
 *           &lt;element ref="{http://www.niso.org/2008/ncip}RequestId"/>
 *         &lt;/choice>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}DeleteRequestFields" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}AddRequestFields" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ItemElementType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}UserElementType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Ext" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "initiationHeader",
    "mandatedAction",
    "userId",
    "authenticationInput",
    "itemId",
    "requestType",
    "requestId",
    "deleteRequestFields",
    "addRequestFields",
    "itemElementType",
    "userElementType",
    "ext"
})
@XmlRootElement(name = "UpdateRequestItem")
@Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
public class UpdateRequestItem {

    @XmlElement(name = "InitiationHeader")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected InitiationHeader initiationHeader;
    @XmlElement(name = "MandatedAction")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected MandatedAction mandatedAction;
    @XmlElement(name = "UserId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected UserId userId;
    @XmlElement(name = "AuthenticationInput")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<AuthenticationInput> authenticationInput;
    @XmlElement(name = "ItemId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected ItemId itemId;
    @XmlElement(name = "RequestType")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SchemeValuePair requestType;
    @XmlElement(name = "RequestId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected RequestId requestId;
    @XmlElement(name = "DeleteRequestFields")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected DeleteRequestFields deleteRequestFields;
    @XmlElement(name = "AddRequestFields")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected AddRequestFields addRequestFields;
    @XmlElement(name = "ItemElementType")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<SchemeValuePair> itemElementType;
    @XmlElement(name = "UserElementType")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<SchemeValuePair> userElementType;
    @XmlElement(name = "Ext")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected Ext ext;

    /**
     * Gets the value of the initiationHeader property.
     * 
     * @return
     *     possible object is
     *     {@link InitiationHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public InitiationHeader getInitiationHeader() {
        return initiationHeader;
    }

    /**
     * Sets the value of the initiationHeader property.
     * 
     * @param value
     *     allowed object is
     *     {@link InitiationHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setInitiationHeader(InitiationHeader value) {
        this.initiationHeader = value;
    }

    /**
     * Gets the value of the mandatedAction property.
     * 
     * @return
     *     possible object is
     *     {@link MandatedAction }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public MandatedAction getMandatedAction() {
        return mandatedAction;
    }

    /**
     * Sets the value of the mandatedAction property.
     * 
     * @param value
     *     allowed object is
     *     {@link MandatedAction }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setMandatedAction(MandatedAction value) {
        this.mandatedAction = value;
    }

    /**
     * Gets the value of the userId property.
     * 
     * @return
     *     possible object is
     *     {@link UserId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public UserId getUserId() {
        return userId;
    }

    /**
     * Sets the value of the userId property.
     * 
     * @param value
     *     allowed object is
     *     {@link UserId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setUserId(UserId value) {
        this.userId = value;
    }

    /**
     * Gets the value of the authenticationInput property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the authenticationInput property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAuthenticationInput().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AuthenticationInput }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<AuthenticationInput> getAuthenticationInput() {
        if (authenticationInput == null) {
            authenticationInput = new ArrayList<AuthenticationInput>();
        }
        return this.authenticationInput;
    }

    /**
     * Gets the value of the itemId property.
     * 
     * @return
     *     possible object is
     *     {@link ItemId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public ItemId getItemId() {
        return itemId;
    }

    /**
     * Sets the value of the itemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link ItemId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setItemId(ItemId value) {
        this.itemId = value;
    }

    /**
     * Gets the value of the requestType property.
     * 
     * @return
     *     possible object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SchemeValuePair getRequestType() {
        return requestType;
    }

    /**
     * Sets the value of the requestType property.
     * 
     * @param value
     *     allowed object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setRequestType(SchemeValuePair value) {
        this.requestType = value;
    }

    /**
     * Gets the value of the requestId property.
     * 
     * @return
     *     possible object is
     *     {@link RequestId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public RequestId getRequestId() {
        return requestId;
    }

    /**
     * Sets the value of the requestId property.
     * 
     * @param value
     *     allowed object is
     *     {@link RequestId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setRequestId(RequestId value) {
        this.requestId = value;
    }

    /**
     * Gets the value of the deleteRequestFields property.
     * 
     * @return
     *     possible object is
     *     {@link DeleteRequestFields }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public DeleteRequestFields getDeleteRequestFields() {
        return deleteRequestFields;
    }

    /**
     * Sets the value of the deleteRequestFields property.
     * 
     * @param value
     *     allowed object is
     *     {@link DeleteRequestFields }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setDeleteRequestFields(DeleteRequestFields value) {
        this.deleteRequestFields = value;
    }

    /**
     * Gets the value of the addRequestFields property.
     * 
     * @return
     *     possible object is
     *     {@link AddRequestFields }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public AddRequestFields getAddRequestFields() {
        return addRequestFields;
    }

    /**
     * Sets the value of the addRequestFields property.
     * 
     * @param value
     *     allowed object is
     *     {@link AddRequestFields }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setAddRequestFields(AddRequestFields value) {
        this.addRequestFields = value;
    }

    /**
     * Gets the value of the itemElementType property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the itemElementType property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getItemElementType().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SchemeValuePair }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<SchemeValuePair> getItemElementType() {
        if (itemElementType == null) {
            itemElementType = new ArrayList<SchemeValuePair>();
        }
        return this.itemElementType;
    }

    /**
     * Gets the value of the userElementType property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the userElementType property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getUserElementType().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SchemeValuePair }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<SchemeValuePair> getUserElementType() {
        if (userElementType == null) {
            userElementType = new ArrayList<SchemeValuePair>();
        }
        return this.userElementType;
    }

    /**
     * Gets the value of the ext property.
     * 
     * @return
     *     possible object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public Ext getExt() {
        return ext;
    }

    /**
     * Sets the value of the ext property.
     * 
     * @param value
     *     allowed object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setExt(Ext value) {
        this.ext = value;
    }

}
