package cz.kpsys.portaro.util.database;

import com.zaxxer.hikari.HikariDataSource;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.databaseproperties.DataSourceFactory;
import cz.kpsys.portaro.databaseproperties.DatabaseConnectionSettings;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class Katx1RecordFieldMigrationDbProcCaller implements Runnable {

    @NonNull DatabaseProperties dbUpdateDatabaseProperties;
    @NonNull Provider<@NonNull ZoneId> databaseColumnsTimeZoneProvider;

    @Override
    public void run() {
        if (!dbUpdateDatabaseProperties.getType().equals(DatabaseConnectionSettings.DBTYPE_FIREBIRD)) {
            return;
        }

        LocalTime now = LocalTime.now(CoreConstants.CZECH_TIME_ZONE_ID);
        if (!now.isAfter(LocalTime.of(23, 30)) && !now.isBefore(LocalTime.of(1, 30))) {
            return;
        }

        try (HikariDataSource dataSource = DataSourceFactory.createDbUpdateDataSource(dbUpdateDatabaseProperties, databaseColumnsTimeZoneProvider.get(), cfg -> {})) {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            TimeMeter tm = TimeMeter.start();
            int rowsCount = 5000;

            log.info("Starting sproc_rozdel_do_record_field with {} rows", rowsCount);
            long processedRecordsCount = Objects.requireNonNull(jdbcTemplate.queryForObject("select count(*) from sproc_rozdel_do_record_field(" + rowsCount + ")", Long.class));
            log.info("Finished sproc_rozdel_do_record_field with {} rows in {}", rowsCount, tm.elapsedTimeString());

            if (processedRecordsCount == 0) {
                log.info("All rows in kat1_1 via sproc_rozdel_do_record_field migrated");
                return;
            }

            log.info("Ending katX_1 -> record_field migration phase");
        }
    }

}
