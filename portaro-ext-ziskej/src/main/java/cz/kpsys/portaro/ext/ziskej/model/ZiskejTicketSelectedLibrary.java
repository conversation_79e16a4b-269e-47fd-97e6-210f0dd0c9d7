package cz.kpsys.portaro.ext.ziskej.model;

import cz.kpsys.portaro.commons.validation.uppercase.Uppercase;
import cz.kpsys.portaro.ext.ziskej.json.LibrarySentTypeZiskejRequest;
import lombok.NonNull;

public record ZiskejTicketSelectedLibrary(

        /**
         * Manuálně zvolená dožádaná knihovna, sigla, velkými písmeny. Je vyplněno pouze do okamžiku schválení a přiřazení DK.
         */
        @NonNull
        @Uppercase
        String sigla,

        /**
         * Upřesnění požadovanéhop způsobu dodání pro manuálně zvolená dožádaná knihovna. Poslat nebo vyzvednout. Jednotlivé hodnoty:
         * sent - Poslat
         * pick - Vyzvednout
         */
        @NonNull
        LibrarySentTypeZiskejRequest lbManualLibrarySent

) {}
