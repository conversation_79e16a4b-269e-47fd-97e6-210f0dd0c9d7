package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Named;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.detail.Field;
import lombok.NonNull;

import java.util.Optional;

public record AcceptableValueFieldValue<VAL extends Identified<?>>(

    @NonNull
    VAL value,

    @NonNull
    String label,

    @NonNull
    Text text

) implements ScalarFieldValue<VAL> {

    public static <VAL extends Identified<?>> AcceptableValueFieldValue<VAL> ofGeneric(@NonNull VAL value) {
        String label = String.valueOf(value.getId());

        Text text = switch (value) {
            case Labeled labeled -> labeled.getText();
            case Named named -> Texts.ofNative(named.getName());
            default -> Texts.ofNative(label);
        };

        return new AcceptableValueFieldValue<>(value, label, text);
    }

    public static @NonNull AcceptableValueFieldValue<LabeledIdentified<String>> ofStringIdentified(LabeledIdentified<String> value) {
        return new AcceptableValueFieldValue<>(
                value,
                value.getId(),
                value.getText()
        );
    }

    public static <R extends Identified<?>> Optional<R> extract(@NonNull Field<AcceptableValueFieldValue<R>> field) {
        return Optional.ofNullable(ObjectUtil.elvis((AcceptableValueFieldValue<R>) field.getValueHolder(), AcceptableValueFieldValue<R>::value));
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, AcceptableValueFieldValue.class, AcceptableValueFieldValue::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }
}
