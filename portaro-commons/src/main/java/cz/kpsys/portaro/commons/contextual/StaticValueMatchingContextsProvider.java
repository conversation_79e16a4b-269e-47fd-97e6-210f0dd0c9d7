package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StaticValueMatchingContextsProvider<CTX, V> implements Provider<Collection<CTX>> {

    @NonNull ValueMatchingContextProvider<CTX, @NonNull V, @NonNull V> valueContextProvider;
    @NonNull V matchingValue;

    @Override
    public Collection<CTX> get() {
        return valueContextProvider.getContextsWith(matchingValue);
    }
}
