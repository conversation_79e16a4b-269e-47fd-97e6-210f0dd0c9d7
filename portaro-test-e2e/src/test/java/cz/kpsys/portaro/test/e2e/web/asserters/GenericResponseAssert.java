package cz.kpsys.portaro.test.e2e.web.asserters;

import io.restassured.response.Response;
import lombok.NonNull;
import lombok.Value;
import lombok.experimental.NonFinal;
import org.springframework.http.HttpStatus;

import static cz.kpsys.portaro.test.e2e.web.utils.ResponseDataExtractor.extractAsString;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Value
@NonFinal
public class GenericResponseAssert {

    @NonNull
    protected Response resp;

    public GenericResponseAssert assertHttpStatusOk() {
        return assertHttpStatus(HttpStatus.OK);
    }

    public GenericResponseAssert assertHttpStatus(HttpStatus status) {
        assertEquals(status.value(), resp.statusCode(), "Http response status is not %s, but %s, body is %s".formatted(status, resp.statusCode(), resp.body()));
        return this;
    }

    public GenericResponseAssert assertHttpStatusIn(HttpStatus.Series statusSeries) {
        assertEquals(statusSeries, HttpStatus.Series.resolve(resp.statusCode()), "Http response status is not in series %s, but %s, body is %s".formatted(statusSeries, resp.statusCode(), resp.body()));
        return this;
    }

    public GenericResponseAssert assertResponseType(String type) {
        assertEquals(type, extractAsString(resp, "responseType"));
        return this;
    }

    public GenericResponseAssert assertResponseTypeWithText(String type, String text) {
        assertEquals(type, extractAsString(resp, "responseType"));
        assertEquals(text, extractAsString(resp, "text"));
        return this;
    }
}