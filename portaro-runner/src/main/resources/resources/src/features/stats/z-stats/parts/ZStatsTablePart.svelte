<script lang="ts">
    import type {ZStatsData} from '../../types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';

    export let statsData: ZStatsData;

    const localize = getLocalization();
</script>

<div class="z-stats-table-part">
    <KpClassicTable additionalClasses="z-stats-table"
                    additionalContainerClasses="z-stats-table-container"
                    colorAccented
                    horizontallyDivided
                    responsive
                    stripedRows
                    hoverRows
                    verticallyCentered
                    horizontallyCentered
                    bordered
                    columnHeadersCentered>

        <tr slot="header">
            <th>{localize(/* @kp-localization commons.rok */ 'commons.rok')}/{localize(/* @kp-localization commons.Mesic */ 'commons.Mesic')}</th>
            <th>{localize(/* @kp-localization statistiky.zServerConnectionString */ 'statistiky.zServerConnectionString')}</th>
            <th>{localize(/* @kp-localization statistiky.pocetHledani */ 'statistiky.pocetHledani')}</th>
            <th>{localize(/* @kp-localization statistiky.pocetUspesnychHledani */ 'statistiky.pocetUspesnychHledani')}</th>
            <th>{localize(/* @kp-localization statistiky.pocetPouzitychZaznamu */ 'statistiky.pocetPouzitychZaznamu')}</th>
        </tr>

        <svelte:fragment slot="body">
            {#each statsData.zClientStatistics as zClientStatistics}
                <tr>
                    <td>{zClientStatistics.year}/{zClientStatistics.month}</td>
                    <td>{zClientStatistics.serverConnectionString}</td>
                    <td>{zClientStatistics.searchCount}</td>
                    <td>{zClientStatistics.successSearchCount} ({zClientStatistics.successSearchPercentageCount})</td>
                    <td>{zClientStatistics.usedRecordsCount} ({zClientStatistics.usedRecordsPercentageCount})</td>
                </tr>
            {/each}
        </svelte:fragment>
    </KpClassicTable>
</div>

<style lang="less">
    .z-stats-table-part {
        width: 100%;
    }
</style>