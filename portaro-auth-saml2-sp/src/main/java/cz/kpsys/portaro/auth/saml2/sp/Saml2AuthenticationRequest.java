package cz.kpsys.portaro.auth.saml2.sp;

import cz.kpsys.portaro.auth.process.AbstractAuthenticationRequest;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistration;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class Saml2AuthenticationRequest extends AbstractAuthenticationRequest<String> {

    @NonNull RelyingPartyRegistration relyingPartyRegistration;
    @NonNull String saml2Response;

    public Saml2AuthenticationRequest(@NonNull Department department,
                                      @NonNull RelyingPartyRegistration relyingPartyRegistration,
                                      @NonNull String saml2Response) {
        super(department);
        this.relyingPartyRegistration = relyingPartyRegistration;
        this.saml2Response = saml2Response;
    }

    @Override
    public String getCredentials() {
        return saml2Response;
    }
}
