/**
 * @ngdoc type
 * @name ValueEditorLocalizations
 * @module portaro.value-editors.localizations
 *
 * @description
 * All value editor localizations must be object with all string typed properties.
 *
 * ```
 *      interface ValueEditorLocalizations {
 *          [key: string]: string;
 *      }
 * ```
 */
export interface ValueEditorLocalizations {
    [key: string]: string;
}

export type EditorsLocalizationFunction<LOC extends ValueEditorLocalizations> = (key: (keyof LOC) & string) => LOC[keyof LOC] | ((keyof LOC) & string);