package cz.kpsys.portaro.form.editedproperty;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.ArrayList;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EditedPropertyList extends ArrayList<EditedProperty<?>> {

    @Nullable String objectName;

    public EditedPropertyList() {
        this(null);
    }


    public EditedPropertyList add(String property, ValueEditor<?, ?, ?> editorHodnoty) {
        Assert.notNull(objectName, "Cannot call add without text directly when object name is not defined");
        return add(property, Texts.ofObjectPropertyMessageCode(objectName, property), editorHodnoty);
    }

    public EditedPropertyList add(String property, Text text, ValueEditor<?, ?, ?> valueEditor) {
        add(EditedProperty.withGeneratedId(property, text, valueEditor));
        return this;
    }

}
