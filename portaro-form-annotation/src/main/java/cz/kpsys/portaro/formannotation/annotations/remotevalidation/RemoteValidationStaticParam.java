package cz.kpsys.portaro.formannotation.annotations.remotevalidation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.ANNOTATION_TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface RemoteValidationStaticParam {

    String name() default "";

    String value() default "";
}
