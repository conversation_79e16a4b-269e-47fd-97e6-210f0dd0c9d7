package cz.kpsys.portaro.formconfig.validation;

import cz.kpsys.portaro.auth.current.CurrentAuthProvider;
import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.object.TypedContextualObjectModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.BeanFactory;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AfterRequiredValidationViolationFormObjectModifier<V> implements TypedContextualObjectModifier<V> {

    @NonNull BeanFactory beanFactory;
    @NonNull CurrentAuthProvider currentAuthProvider;

    @Override
    public V modify(V formObject, Department department) {
        Optional<AfterIntegrityValidationViolation> annotation = AnnotationUtil.findClassLevelAnnotation(formObject, AfterIntegrityValidationViolation.class);
        if (annotation.isEmpty()) {
            return formObject;
        }

        TypedAuthenticatedContextualObjectModifier<V> objectModifier = beanFactory.getBean(annotation.get().bean(), TypedAuthenticatedContextualObjectModifier.class);
        return objectModifier.modify(formObject, department, currentAuthProvider.getCurrentAuth());
    }

}
